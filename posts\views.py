from flask import Blueprint, render_template, flash, redirect, url_for, request
from flask_login import current_user, login_required

from config import db, Post, roles_required, logger, encrypt, decrypt
from posts.forms import PostForm
from sqlalchemy import desc

posts_bp = Blueprint('posts', __name__, template_folder='templates')


@posts_bp.route('/posts')
@login_required
@roles_required('end_user')
def posts():
    all_posts = Post.query.order_by(desc('id')).all()
    #posts = [p.decrypt() for p in all_posts]
    for post in all_posts:
        post.decrypt()
    return render_template('posts/posts.html', posts=all_posts)


@posts_bp.route('/create', methods=('GET', 'POST'))
@login_required
@roles_required('end_user')
def create():
    form = PostForm()

    if form.validate_on_submit():
        new_post = Post(userid=current_user.get_id(), title=form.title.data, body=form.body.data)
        db.session.add(new_post)
        db.session.commit()

        logger.warning('[User:{}, Role:{}, Post:{}, IP:{}] Post Created'.format(
            current_user.email,
            current_user.role,
            new_post.id,
            request.remote_addr))

        flash('Post created', category='success')
        return posts()

    return render_template('posts/create.html', form=form)


@posts_bp.route('/<int:id>/update', methods=('GET', 'POST'))
@login_required
@roles_required('end_user')
def update(id):
    post_to_update = Post.query.filter_by(id=id).first()

    if current_user.get_id() != post_to_update.user.get_id():
        flash('You are not authorised to update this post', category="info")
        return redirect(url_for('posts.posts'))

    if not post_to_update:
        return posts()

    form = PostForm()

    if form.validate_on_submit():
        post_to_update.update(title=form.title.data, body=form.body.data)

        flash('Post updated', category='success')
        return posts()

    post_to_update.decrypt()

    form.title.data = post_to_update.title
    form.body.data = post_to_update.body

    logger.warning('[User:{}, Role:{}, Post:{}, Author:{} IP:{}] Post Updated'.format(
        current_user.email,
        current_user.role,
        post_to_update.id,
        post_to_update.user.email,
        request.remote_addr))

    return render_template('posts/update.html', form=form)


@posts_bp.route('/<int:id>/delete')
@login_required
@roles_required('end_user')
def delete(id):
    post_to_delete = Post.query.filter_by(id=id).first()

    if current_user.get_id() != post_to_delete.user.get_id():
        flash('You are not authorised to delete this post', category="info")
        return redirect(url_for('posts.posts'))

    logger.warning('[User:{}, Role:{}, Post:{}, Author:{}, IP:{}] Post Deleted'.format(
        current_user.email,
        current_user.role,
        post_to_delete.id,
        post_to_delete.user.email,
        request.remote_addr))

    Post.query.filter_by(id=id).delete()
    db.session.commit()

    flash('Post deleted', category='success')
    return posts()
