#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Flask Security Features Black-box Testing Launcher

This script is a simple launcher for running the security test suite.
It uses pytest commands to run tests directly.
"""

import sys
import os
import argparse
import subprocess
import time
import platform
import requests
from urllib3.exceptions import InsecureRequestWarning

# Disable insecure request warnings
requests.packages.urllib3.disable_warnings(InsecureRequestWarning)

def parse_args():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(description='Flask Security Features Black-box Testing Tool')

    parser.add_argument('--test-type', '-t', nargs='+', choices=['all', 'task16', 'task17', 'task18', 'task19', 'task20'],
                        help='Test types to run, multiple can be specified. all=all tests, task16=symmetric encryption, task17=hardcoded data, task18=error handling, task19=firewall rules, task20=security headers')

    parser.add_argument('--url', '-u', default='auto',
                        help='Flask application URL, default is auto (auto-detect HTTP/HTTPS), can also specify like http://127.0.0.1:5000 or https://127.0.0.1:5000')

    parser.add_argument('--junit-xml',
                        help='Generate JUnit XML format test report')

    parser.add_argument('--html',
                        help='Generate HTML format test report')

    parser.add_argument('--skip-app-check', action='store_true',
                        help='Skip Flask application running check')
    
    return parser.parse_args()

def print_system_info():
    """Print system information"""
    print("=" * 60)
    print("Flask Security Features Black-box Testing Tool")
    print("=" * 60)
    print(f"Operating System: {platform.system()} {platform.release()}")
    print(f"Python Version: {platform.python_version()}")
    print(f"Test Time: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)

def auto_detect_protocol():
    """Auto-detect Flask application protocol (HTTP or HTTPS)"""
    base_urls = [
        'https://127.0.0.1:5000',  # Try HTTPS first
        'http://127.0.0.1:5000',   # Then try HTTP
        'https://localhost:5000',
        'http://localhost:5000'
    ]

    print("🔍 Auto-detecting Flask application protocol...")

    for url in base_urls:
        try:
            print(f"  Trying connection: {url}")
            response = requests.get(url, verify=False, timeout=3)
            if response.status_code < 500:  # Accept any non-server error response
                print(f"  ✓ Detection successful! Using: {url}")
                return url
        except requests.exceptions.ConnectionError:
            print(f"  ✗ Connection failed: {url}")
            continue
        except requests.exceptions.Timeout:
            print(f"  ✗ Connection timeout: {url}")
            continue
        except Exception as e:
            print(f"  ✗ Connection error: {url} - {e}")
            continue

    print("  ⚠️  Auto-detection failed, using default HTTPS")
    return 'https://127.0.0.1:5000'

def check_app_running(url):
    """Check if Flask application is running"""
    # If URL is 'auto', perform auto-detection
    if url == 'auto':
        url = auto_detect_protocol()

    print(f"Checking if Flask application is running at {url}...")
    try:
        response = requests.get(url, verify=False, timeout=5)
        print(f"✓ Flask application is running! Status code: {response.status_code}")
        return url  # Return the actual URL used
    except requests.exceptions.ConnectionError:
        print("✗ Unable to connect to Flask application. Please ensure the application is started and running at the specified URL.")
        print("\nPossible solutions:")
        print("1. Start Flask application:")
        print("   - Set environment variable: export FLASK_APP=app.py (Linux/Mac) or set FLASK_APP=app.py (Windows)")
        print("   - Run: flask run --cert=cert.pem --key=key.pem (HTTPS) or flask run (HTTP)")
        print("2. Use --url parameter to specify correct URL:")
        print("   - Example: python run_tests.py --url http://localhost:5000")
        print("3. Use --skip-app-check parameter to skip this check:")
        print("   - Example: python run_tests.py --skip-app-check")
        return None
    except requests.exceptions.Timeout:
        print("✗ Connection to Flask application timed out. Please check if the application is running normally.")
        return None
    except Exception as e:
        print(f"✗ Error checking Flask application: {e}")
        return None

def run_tests(args, actual_url=None):
    """Run tests"""
    # Get current script directory
    current_dir = os.path.dirname(os.path.abspath(__file__))
    test_dir = os.path.join(current_dir, 'security_tests')

    # Build pytest command arguments
    pytest_args = ['pytest', '-v']

    # If test type is specified, only run corresponding test files
    if args.test_type:
        test_files = []
        for test_type in args.test_type:
            if test_type == 'all':
                test_files = []  # Run all tests
                break
            elif test_type == 'task16':
                test_files.append('test_task16_symmetric_encryption.py')
            elif test_type == 'task17':
                test_files.append('test_task17_hardcoded_data.py')
            elif test_type == 'task18':
                test_files.append('test_task18_error_handling.py')
            elif test_type == 'task19':
                test_files.append('test_task19_firewall_rules.py')
            elif test_type == 'task20':
                test_files.append('test_task20_security_headers.py')

        # If test files are specified, add to pytest arguments
        if test_files:
            for test_file in test_files:
                pytest_args.append(os.path.join(test_dir, test_file))
        else:
            # Run all tests
            pytest_args.append(test_dir)
    else:
        # Default to run all tests
        pytest_args.append(test_dir)
    
    # Add output options
    if args.junit_xml:
        pytest_args.extend(['--junitxml', args.junit_xml])

    if args.html:
        pytest_args.extend(['--html', args.html, '--self-contained-html'])

    # Set environment variables
    env = os.environ.copy()
    # Use actually detected URL, if not available use command line argument
    test_url = actual_url if actual_url else args.url
    if test_url == 'auto':
        test_url = 'https://127.0.0.1:5000'  # Default value
    env['FLASK_TEST_URL'] = test_url
    print(f"Using test URL: {test_url}")

    # Run tests
    print(f"Running tests, command: {' '.join(pytest_args)}")
    try:
        result = subprocess.run(pytest_args, env=env, check=False)
        return result.returncode
    except Exception as e:
        print(f"Failed to run tests: {e}")
        return 1

def main():
    """Main function"""
    # Print system information
    print_system_info()

    # Parse command line arguments
    args = parse_args()

    # Check if Flask application is running and get actual URL
    actual_url = args.url
    if not args.skip_app_check:
        detected_url = check_app_running(args.url)
        if detected_url:
            actual_url = detected_url
        else:
            choice = input("\nDo you still want to continue running tests? (y/n): ").strip().lower()
            if choice != 'y':
                print("Tests cancelled.")
                return 1
            # If user chooses to continue but detection failed, use original URL or default URL
            if args.url == 'auto':
                actual_url = 'https://127.0.0.1:5000'

    # Run tests
    result = run_tests(args, actual_url)

    # Return result code
    return result

if __name__ == '__main__':
    sys.exit(main()) 