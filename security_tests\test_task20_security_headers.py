"""
Task 20. Security Headers - Security Headers Black-box Testing

This test file implements complete black-box testing for Canvas exercise, including:

Test Functions:
1. Detection Item I: Verify security headers are implemented using Talisman library
2. Detection Item II: Verify CSP configuration allows reCAPTCHA, Bootstrap CSS and JS

Black-box Testing Features:
- Uses hybrid detection method for Talisman (source code check + behavior verification)
- Uses complete black-box method for CSP (HTTP response header analysis)
- Precisely verifies specific external resource URLs required by task
- Intelligent CSP parsing, independent of variable names
- Highly universal, applicable to different configuration methods

Detection Methods:
- Talisman verification: Source code check + HTTP response header verification
- CSP behavioral testing: Detect CSP configuration through HTTP response headers
- External resource verification: Check specific URLs for reCAPTCHA and Bootstrap
- Security header integrity check: Verify all necessary security headers

Scoring Standards:
- Must use Talisman library (explicitly required by task)
- CSP must contain 4 reCAPTCHA URLs
- CSP must contain complete paths for Bootstrap CSS and JS
- Security headers correctly set in HTTP responses
"""

import re
import requests
from bs4 import BeautifulSoup


class TestTask20SecurityHeaders:
    """Task 20 Security Headers Detection Test Class"""

    def test_01_talisman_implementation(self, config_file_paths, http_client, app_url):
        """
        Detection Item I: Verify security headers are implemented using Talisman library

        Corresponding Requirements:
        In config.py or app.py file, ensure security headers are implemented using Talisman library.
        Look for code like:
        talisman = Talisman(app, content_security_policy=csp)

        Hybrid Detection Methods:
        1. Source code check - verify Talisman library import and usage (explicitly required by task)
        2. Behavioral verification - check Talisman characteristics in HTTP response headers
        3. Functional testing - verify actual security effects of Talisman
        """
        print(f"\n🔍 Task 20.I - Detecting Talisman library implementation")

        # Step 1: Source code check (task explicitly requires Talisman library)
        talisman_source_check = self._check_talisman_in_source(config_file_paths)

        # Step 2: Behavioral verification (check HTTP response headers)
        talisman_behavior_check = self._check_talisman_behavior(http_client, app_url)

        # Comprehensive assessment
        if talisman_source_check and talisman_behavior_check:
            print(f"✅ Talisman library detection passed - both source code and behavioral verification successful")
        elif talisman_source_check:
            print(f"⚠️  Talisman library found in source code, but behavioral verification partially failed")
            print(f"✅ Still passes test - source code check is the main requirement")
        else:
            assert False, "Talisman library usage not detected"

    def test_02_csp_configuration_blackbox(self, http_client, app_url):
        """
        Detection Item II: Verify CSP configuration through complete black-box method

        Corresponding Requirements:
        Ensure a content security policy (csp) is defined that allows execution of external libraries
        for reCAPTCHA, bootstrap CSS and bootstrap JS. Should see 4 URLs for reCAPTCHA.

        Complete Black-box Detection Methods:
        1. HTTP response header analysis - get CSP header content
        2. CSP directive parsing - analyze style-src, script-src, frame-src
        3. External resource verification - check reCAPTCHA and Bootstrap URLs
        4. Precise matching verification - verify specific configuration required by task
        """
        print(f"\n🔍 Task 20.II - Detecting CSP configuration (complete black-box method)")

        # Get CSP header
        csp_header = self._get_csp_header(http_client, app_url)
        if not csp_header:
            assert False, "Content-Security-Policy not found in HTTP response headers"

        print(f"📋 Found CSP header: {len(csp_header)} characters")

        # Parse CSP directives
        csp_directives = self._parse_csp_directives(csp_header)
        print(f"📊 Parsed {len(csp_directives)} CSP directives")

        # Verify required directives exist
        required_directives = ['style-src', 'script-src', 'frame-src']
        missing_directives = [d for d in required_directives if d not in csp_directives]
        if missing_directives:
            assert False, f"CSP missing required directives: {missing_directives}"

        # Verify reCAPTCHA URLs (task requires 4 URLs)
        recaptcha_check = self._verify_recaptcha_urls(csp_directives)

        # Verify Bootstrap resources
        bootstrap_check = self._verify_bootstrap_resources(csp_directives)

        # Comprehensive assessment
        if recaptcha_check and bootstrap_check:
            print(f"✅ CSP configuration detection passed - all external resources correctly configured")
        else:
            issues = []
            if not recaptcha_check:
                issues.append("reCAPTCHA URL configuration")
            if not bootstrap_check:
                issues.append("Bootstrap resource configuration")
            assert False, f"CSP configuration issues: {', '.join(issues)}"

    def _check_talisman_in_source(self, config_file_paths):
        """Check Talisman usage in source code"""
        if not config_file_paths:
            print(f"⚠️  Configuration files not found, skipping source code check")
            return False

        talisman_imported = False
        talisman_initialized = False

        for file_path in config_file_paths:
            try:
                with open(file_path, 'r', encoding='utf-8') as file:
                    content = file.read()

                # Check Talisman import
                import_patterns = [
                    r'from\s+flask_talisman\s+import\s+Talisman',
                    r'import\s+flask_talisman',
                    r'from\s+flask_talisman\s+import\s+\*'
                ]

                for pattern in import_patterns:
                    if re.search(pattern, content, re.IGNORECASE):
                        talisman_imported = True
                        print(f"✅ Found Talisman import in {file_path}")
                        break

                # Check Talisman initialization
                init_patterns = [
                    r'Talisman\s*\(\s*app',
                    r'talisman\s*=\s*Talisman',
                    r'Talisman\s*\(\s*[^)]*app[^)]*\)'
                ]

                for pattern in init_patterns:
                    if re.search(pattern, content, re.IGNORECASE):
                        talisman_initialized = True
                        print(f"✅ Found Talisman initialization in {file_path}")
                        break

            except Exception as e:
                print(f"⚠️  Error reading file {file_path}: {str(e)}")

        source_check_passed = talisman_imported and talisman_initialized
        if source_check_passed:
            print(f"✅ Source code check passed - Talisman library correctly imported and initialized")
        else:
            print(f"❌ Source code check failed - Import: {talisman_imported}, Initialization: {talisman_initialized}")

        return source_check_passed

    def _check_talisman_behavior(self, http_client, app_url):
        """Check Talisman behavioral characteristics"""
        try:
            response = http_client.get(app_url, allow_redirects=True)
            headers = response.headers

            # Talisman typically sets these security headers
            talisman_headers = [
                'Content-Security-Policy',
                'X-Content-Type-Options',
                'X-Frame-Options',
                'Strict-Transport-Security'
            ]

            found_headers = []
            for header in talisman_headers:
                if any(h.lower() == header.lower() for h in headers):
                    found_headers.append(header)

            print(f"🛡️  Found {len(found_headers)}/{len(talisman_headers)} Talisman characteristic headers")
            print(f"📋 Found security headers: {found_headers}")

            # Need to find at least 2 security headers to consider Talisman working
            behavior_check_passed = len(found_headers) >= 2

            if behavior_check_passed:
                print(f"✅ Behavioral check passed - Talisman is setting security headers")
            else:
                print(f"⚠️  Behavioral check partially failed - insufficient security headers")

            return behavior_check_passed

        except Exception as e:
            print(f"❌ Behavioral check exception: {str(e)}")
            return False

    def _get_csp_header(self, http_client, app_url):
        """Get CSP header content"""
        try:
            response = http_client.get(app_url, allow_redirects=True)
            headers = response.headers

            # Find CSP header (case insensitive)
            csp_header = None
            for header_name, header_value in headers.items():
                if header_name.lower() == 'content-security-policy':
                    csp_header = header_value
                    break

            if csp_header:
                print(f"📋 Found CSP header: {header_name}")
                return csp_header
            else:
                print(f"❌ Content-Security-Policy header not found")
                return None

        except Exception as e:
            print(f"❌ Error getting CSP header: {str(e)}")
            return None

    def _parse_csp_directives(self, csp_header):
        """Parse CSP directives"""
        directives = {}

        # CSP format: directive1 source1 source2; directive2 source3 source4;
        directive_parts = csp_header.split(';')

        for part in directive_parts:
            part = part.strip()
            if not part:
                continue

            # Split directive name and source list
            tokens = part.split()
            if len(tokens) < 1:
                continue

            directive_name = tokens[0]
            sources = tokens[1:] if len(tokens) > 1 else []

            directives[directive_name] = sources
            print(f"  📝 {directive_name}: {len(sources)} sources")

        return directives

    def _verify_recaptcha_urls(self, csp_directives):
        """Verify reCAPTCHA URL configuration (task requires 4 URLs)"""
        print(f"\n🔍 Verifying reCAPTCHA URL configuration")

        # 4 reCAPTCHA URLs required by task
        required_recaptcha_urls = [
            'https://www.google.com/recaptcha/',
            'https://www.gstatic.com/recaptcha/',
            'https://www.google.com/recaptcha/',  # Duplicate in script-src
            'https://recaptcha.google.com/recaptcha/'
        ]

        # Deduplicated required URLs
        unique_required_urls = [
            'https://www.google.com/recaptcha/',
            'https://www.gstatic.com/recaptcha/',
            'https://recaptcha.google.com/recaptcha/'
        ]

        found_urls = []

        # Check script-src and frame-src directives
        for directive in ['script-src', 'frame-src']:
            if directive in csp_directives:
                sources = csp_directives[directive]
                for source in sources:
                    for required_url in unique_required_urls:
                        if required_url in source:
                            found_urls.append(f"{directive}: {source}")

        print(f"📋 Found reCAPTCHA URLs:")
        for url in found_urls:
            print(f"  ✅ {url}")

        # Check if at least found main reCAPTCHA domains
        google_recaptcha_found = any('google.com/recaptcha' in url for url in found_urls)
        gstatic_recaptcha_found = any('gstatic.com/recaptcha' in url for url in found_urls)
        recaptcha_google_found = any('recaptcha.google.com' in url for url in found_urls)

        recaptcha_check_passed = google_recaptcha_found and (gstatic_recaptcha_found or recaptcha_google_found)

        if recaptcha_check_passed:
            print(f"✅ reCAPTCHA URL verification passed")
        else:
            print(f"❌ reCAPTCHA URL verification failed")
            print(f"  Google reCAPTCHA: {google_recaptcha_found}")
            print(f"  Gstatic reCAPTCHA: {gstatic_recaptcha_found}")
            print(f"  reCAPTCHA Google: {recaptcha_google_found}")

        return recaptcha_check_passed

    def _verify_bootstrap_resources(self, csp_directives):
        """Verify Bootstrap resource configuration"""
        print(f"\n🔍 Verifying Bootstrap resource configuration")

        # Check Bootstrap CSS (in style-src)
        bootstrap_css_found = False
        if 'style-src' in csp_directives:
            sources = csp_directives['style-src']
            for source in sources:
                if 'bootstrap' in source.lower() and 'css' in source.lower():
                    bootstrap_css_found = True
                    print(f"  ✅ Bootstrap CSS: {source}")
                    break
                elif 'cdn.jsdelivr.net' in source and 'bootstrap' in source:
                    bootstrap_css_found = True
                    print(f"  ✅ Bootstrap CSS (CDN): {source}")
                    break

        # Check Bootstrap JS (in script-src)
        bootstrap_js_found = False
        if 'script-src' in csp_directives:
            sources = csp_directives['script-src']
            for source in sources:
                if 'bootstrap' in source.lower() and 'js' in source.lower():
                    bootstrap_js_found = True
                    print(f"  ✅ Bootstrap JS: {source}")
                    break
                elif 'cdn.jsdelivr.net' in source and 'bootstrap' in source:
                    bootstrap_js_found = True
                    print(f"  ✅ Bootstrap JS (CDN): {source}")
                    break

        bootstrap_check_passed = bootstrap_css_found and bootstrap_js_found

        if bootstrap_check_passed:
            print(f"✅ Bootstrap resource verification passed")
        else:
            print(f"❌ Bootstrap resource verification failed")
            print(f"  Bootstrap CSS: {bootstrap_css_found}")
            print(f"  Bootstrap JS: {bootstrap_js_found}")

        return bootstrap_check_passed
