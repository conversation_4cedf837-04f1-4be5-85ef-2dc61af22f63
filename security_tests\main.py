#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Flask Security Features Black-Box Testing Entry Program

This program provides a simple command-line interface to run security tests against Flask applications.
"""

import argparse
import sys
import os
import time
import platform
import subprocess
from pathlib import Path

# Add current directory to import path to ensure modules can be found
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

def get_test_dir():
    """Get the test directory path"""
    return os.path.dirname(os.path.abspath(__file__))

def run_tests(args):
    """
    Run the specified tests
    
    Parameters:
    - args: Command line arguments object
    """
    test_dir = get_test_dir()
    
    # Build pytest command arguments
    pytest_args = ['pytest', '-v']  # Use pytest command
    
    # If test types are specified, only run the corresponding test files
    if args.test_type:
        test_files = []
        for test_type in args.test_type:
            if test_type == 'all':
                test_files = []  # Run all tests
                break
            elif test_type == 'encryption':
                test_files.append('test_symmetric_encryption.py')
            elif test_type == 'env':
                test_files.append('test_env_config.py')
            elif test_type == 'error':
                test_files.append('test_error_handling.py')
            elif test_type == 'firewall':
                test_files.append('test_firewall_rules.py')
            elif test_type == 'headers':
                test_files.append('test_security_headers.py')
        
        # If test files are specified, add them to pytest arguments
        if test_files:
            for test_file in test_files:
                pytest_args.append(os.path.join(test_dir, test_file))
        else:
            # Run all tests
            pytest_args.append(test_dir)
    else:
        # Default to running all tests
        pytest_args.append(test_dir)
    
    # Add output options
    if args.junit_xml:
        pytest_args.extend(['--junitxml', args.junit_xml])
    
    if args.html:
        pytest_args.extend(['--html', args.html, '--self-contained-html'])
    
    # Set environment variables
    env = os.environ.copy()
    env['FLASK_TEST_URL'] = args.url
    
    # Run tests
    print(f"Running tests, command: {' '.join(pytest_args)}")
    try:
        result = subprocess.run(pytest_args, env=env, check=False)
        return result.returncode
    except Exception as e:
        print(f"Failed to run tests: {e}")
        return 1

def parse_args():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(description='Flask Security Features Black-Box Testing Tool')
    
    parser.add_argument('--test-type', '-t', nargs='+', choices=['all', 'encryption', 'env', 'error', 'firewall', 'headers'],
                        help='Type of tests to run, can specify multiple. all=all tests, encryption=symmetric encryption, env=environment variable configuration, error=error handling, firewall=firewall rules, headers=security headers')
    
    parser.add_argument('--url', '-u', default='https://127.0.0.1:5000',
                        help='URL of the Flask application, default is https://127.0.0.1:5000')
    
    parser.add_argument('--junit-xml', 
                        help='Generate JUnit XML format test report')
    
    parser.add_argument('--html',
                        help='Generate HTML format test report')
    
    return parser.parse_args()

def print_system_info():
    """Print system information"""
    print("=" * 60)
    print("Flask Security Features Black-Box Testing Tool")
    print("=" * 60)
    print(f"Operating System: {platform.system()} {platform.release()}")
    print(f"Python Version: {platform.python_version()}")
    print(f"Test Time: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)

def main():
    """Main function"""
    # Print system information
    print_system_info()
    
    # Parse command line arguments
    args = parse_args()
    
    # Run tests
    result = run_tests(args)
    
    # Return result code
    return result

if __name__ == '__main__':
    sys.exit(main()) 