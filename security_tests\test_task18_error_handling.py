"""
Task 18. Error Handling - Error Handling Black-box Testing

This test file implements complete black-box testing for Canvas Exercise 20, including:

Test Functions:
1. Detection Item I: Verify that templates or templates/errors folder contains necessary HTML error page templates
2. Detection Item II: Verify error handling functions are correctly implemented through behavioral testing

Black-box Testing Features:
- Completely independent of specific framework implementations or code patterns
- Verify error handling behavior through actual HTTP requests
- Check existence and content of template files in file system
- Verify actual display effects of error pages
- Highly universal, applicable to different web frameworks

Detection Methods:
- Template file checking: Verify existence and content quality of error template files
- Behavioral testing: Verify error handling functionality by triggering various error states
- Response verification: Check HTTP status codes and error page content
- Content analysis: Verify error pages contain appropriate error descriptions

Scoring Standards:
- All necessary error template files exist (400, 404, 500, 501)
- Error templates contain appropriate error names and descriptions
- Actual HTTP requests can trigger correct error handling
- Error pages display correctly in browser
"""

import pytest
import re
import os
import requests
from pathlib import Path
from bs4 import BeautifulSoup


class TestTask18ErrorHandling:
    """Task 18 Error Handling Detection Test Class"""

    def test_01_error_templates_exist_and_content(self, error_template_paths):
        """
        Detection Item I: Verify error page templates exist and contain appropriate content

        Corresponding Requirements:
        The templates or templates/errors folder should contain HTML error page/templates for the following errors:
        - Bad Request (400)
        - Not Found (404)
        - Internal Server Error (500)
        - Not Implemented (501)

        HTML error page templates should contain error names and one to two paragraphs explaining the meaning of the error.

        Black-box Testing Methods:
        1. File system check - verify template file existence
        2. Content analysis - check templates contain appropriate error descriptions
        3. Structure validation - ensure templates are valid HTML
        """
        print(f"\n🔍 Task 18.I - Detecting error page templates")

        # Check if any error templates were found
        assert error_template_paths, "Error page template files not found"
        print(f"📁 Found {len(error_template_paths)} template files")

        # Check required error codes
        required_error_codes = ['400', '404', '500', '501']
        found_error_codes = set()

        # Error description keyword mapping
        error_descriptions = {
            '400': ['bad request', 'invalid request', 'malformed', 'syntax'],
            '404': ['not found', 'does not exist', 'cannot find', 'missing'],
            '500': ['internal server error', 'server error', 'internal error', 'server problem'],
            '501': ['not implemented', 'not supported', 'unsupported', 'unavailable']
        }

        template_analysis = {}

        for template_path in error_template_paths:
            template_name = Path(template_path).name
            print(f"\n📄 Analyzing template: {template_name}")

            # Extract error code
            error_code = None
            for code in required_error_codes:
                if code in template_name:
                    error_code = code
                    found_error_codes.add(code)
                    break

            if not error_code:
                print(f"  ⚠️  Unable to identify error code: {template_name}")
                continue

            # Read template content
            try:
                with open(template_path, 'r', encoding='utf-8') as file:
                    content = file.read()

                # Check if it's valid HTML
                soup = BeautifulSoup(content, 'html.parser')
                has_html_structure = bool(soup.find('html') or soup.find('body') or soup.find('div'))

                # Check if contains appropriate error descriptions
                content_lower = content.lower()
                found_descriptions = [desc for desc in error_descriptions[error_code]
                                    if desc in content_lower]

                # Check content length (should have explanatory text)
                text_content = soup.get_text().strip()
                has_sufficient_content = len(text_content) > 50  # At least 50 characters of description

                template_analysis[error_code] = {
                    'path': template_path,
                    'has_html_structure': has_html_structure,
                    'found_descriptions': found_descriptions,
                    'has_sufficient_content': has_sufficient_content,
                    'content_length': len(text_content)
                }

                print(f"  ✅ Error code: {error_code}")
                print(f"  📝 HTML structure: {'Yes' if has_html_structure else 'No'}")
                print(f"  📖 Found descriptions: {found_descriptions}")
                print(f"  📏 Content length: {len(text_content)} characters")

                # Validate template quality
                if not found_descriptions:
                    assert False, f"Error template {template_name} does not contain appropriate error description keywords"

                if not has_sufficient_content:
                    assert False, f"Error template {template_name} has insufficient content, should contain explanatory text"

            except Exception as e:
                assert False, f"Error reading template file {template_path}: {str(e)}"

        # Check if any required error code templates are missing
        missing_codes = set(required_error_codes) - found_error_codes
        if missing_codes:
            assert False, f"Missing templates for the following error codes: {', '.join(missing_codes)}"

        print(f"\n✅ Task 18.I detection passed - All necessary error templates exist and have appropriate content")

    def test_02_error_handling_behavior(self, http_client, app_url):
        """
        Detection Item II: Verify error handling functions are correctly implemented through behavioral testing

        Corresponding Requirements:
        Ensure error handling functions exist for each error, like:
        @app.errorhandler(400)
        def bad_request(e):
            return render_template('errors/400.html'), 400

        Black-box Testing Methods:
        1. Behavioral testing - trigger various error states through actual HTTP requests
        2. Response verification - check returned status codes and error page content
        3. Content analysis - verify error pages display correct error information
        4. Template rendering verification - ensure correct error templates are used
        """
        print(f"\n🔍 Task 18.II - Detecting error handling behavior")

        error_tests = [
            {
                'code': 404,
                'description': 'Not Found Error',
                'test_method': self._test_404_error,
                'expected_keywords': ['not found', 'does not exist', '404']
            },
            {
                'code': 400,
                'description': 'Bad Request',
                'test_method': self._test_400_error,
                'expected_keywords': ['bad request', 'invalid', '400']
            },
            {
                'code': 500,
                'description': 'Internal Server Error',
                'test_method': self._test_500_error,
                'expected_keywords': ['internal server error', 'server error', '500']
            },
            {
                'code': 501,
                'description': 'Not Implemented',
                'test_method': self._test_501_error,
                'expected_keywords': ['not implemented', 'not supported', '501']
            }
        ]

        successful_tests = 0

        for error_test in error_tests:
            print(f"\n📋 Testing {error_test['code']} - {error_test['description']}")

            try:
                response = error_test['test_method'](http_client, app_url)

                if response is not None:
                    # Verify status code - more flexible verification logic
                    expected_code = error_test['code']
                    status_code_ok = False

                    if response.status_code == expected_code:
                        print(f"  ✅ Status code exact match: {response.status_code}")
                        status_code_ok = True
                    elif response.status_code >= 400:
                        print(f"  ⚠️  Status code not exact match but still error status: expected {expected_code}, actual {response.status_code}")
                        # For certain errors, accept similar error status codes
                        if (expected_code == 501 and response.status_code == 405) or \
                           (expected_code == 400 and response.status_code in [400, 405, 422]):
                            status_code_ok = True
                            print(f"  ✅ Accepting similar error status code")
                    else:
                        print(f"  ❌ Status code is not error status: {response.status_code}")

                    # Verify response content
                    soup = BeautifulSoup(response.text, 'html.parser')
                    page_text = soup.get_text().lower()

                    # Check if contains expected keywords
                    found_keywords = [kw for kw in error_test['expected_keywords']
                                    if kw in page_text]

                    # More lenient keyword check - also accept general error keywords
                    general_error_keywords = ['error', 'not found', 'bad request', 'invalid', 'not allowed', 'not supported']
                    found_general = [kw for kw in general_error_keywords if kw in page_text]

                    content_ok = False
                    if found_keywords:
                        print(f"  ✅ Found expected keywords: {found_keywords}")
                        content_ok = True
                    elif found_general:
                        print(f"  ✅ Found general error keywords: {found_general}")
                        content_ok = True
                    else:
                        print(f"  ⚠️  No error-related keywords found")
                        print(f"  📄 Page content preview: {page_text[:200]}...")

                    # If either status code or content meets requirements, consider test successful
                    if status_code_ok or content_ok:
                        successful_tests += 1
                        print(f"  ✅ Error handling test passed")
                    else:
                        print(f"  ❌ Error handling test failed")

                else:
                    print(f"  ❌ Unable to get {error_test['code']} error response")

            except Exception as e:
                print(f"  ❌ Exception occurred while testing {error_test['code']} error: {str(e)}")

        # Require at least 2 error handlers working normally (at least 2 out of 4 tests successful)
        assert successful_tests >= 2, f"Error handling test failed, only {successful_tests}/4 tests successful"

        print(f"\n✅ Task 18.II detection passed - {successful_tests}/4 error handling tests successful")

    def _test_404_error(self, http_client, app_url):
        """Test 404 error handling"""
        try:
            # Access a non-existent page
            response = http_client.get(f"{app_url}/nonexistent_page_12345", allow_redirects=False)
            print(f"    📡 Request: GET /nonexistent_page_12345")
            print(f"    📊 Response status: {response.status_code}")

            # Ensure response object is returned even if status code is not expected
            return response

        except requests.exceptions.ConnectionError:
            print(f"    ❌ Connection error, please ensure application is running")
            return None
        except Exception as e:
            print(f"    ❌ Request exception: {str(e)}")
            return None

    def _test_400_error(self, http_client, app_url):
        """Test 400 error handling"""
        try:
            # Method 1: Try sending malformed request
            response = http_client.get(f"{app_url}/?invalid_param=" + "x" * 10000, allow_redirects=False)
            print(f"    📡 Request: GET /?invalid_param=<long string>")
            print(f"    📊 Response status: {response.status_code}")

            # If first attempt didn't get error status, try other methods
            if response.status_code < 400:
                # Method 2: Send invalid POST data
                try:
                    headers = {'Content-Type': 'application/json'}
                    invalid_json = '{"invalid": json data without quotes}'
                    response = http_client.post(f"{app_url}/", headers=headers, data=invalid_json, allow_redirects=False)
                    print(f"    📡 Fallback request: POST / with invalid JSON")
                    print(f"    📊 Response status: {response.status_code}")
                except:
                    pass

            # If still no error status, try accessing endpoint with wrong method
            if response.status_code < 400:
                # Method 3: Try accessing login page with wrong method
                response = http_client.post(f"{app_url}/login", data="", allow_redirects=False)
                print(f"    📡 Fallback request: POST /login without proper data")
                print(f"    📊 Response status: {response.status_code}")

            return response

        except requests.exceptions.ConnectionError:
            print(f"    ❌ Connection error, please ensure application is running")
            return None
        except Exception as e:
            print(f"    ❌ Request exception: {str(e)}")
            return None

    def _test_501_error(self, http_client, app_url):
        """Test 501 error handling"""
        try:
            # Method 1: Use uncommon HTTP methods
            response = http_client.request('PATCH', app_url, allow_redirects=False)
            print(f"    📡 Request: PATCH {app_url}")
            print(f"    📊 Response status: {response.status_code}")

            # If didn't get 501, try other methods
            if response.status_code != 501:
                # Method 2: Try TRACE method
                response = http_client.request('TRACE', app_url, allow_redirects=False)
                print(f"    📡 Fallback request: TRACE {app_url}")
                print(f"    📊 Response status: {response.status_code}")

            # If still no 501, try other uncommon methods
            if response.status_code != 501:
                # Method 3: Try CONNECT method
                try:
                    response = http_client.request('CONNECT', app_url, allow_redirects=False)
                    print(f"    📡 Fallback request: CONNECT {app_url}")
                    print(f"    📊 Response status: {response.status_code}")
                except:
                    pass

            return response

        except requests.exceptions.ConnectionError:
            print(f"    ❌ Connection error, please ensure application is running")
            return None
        except Exception as e:
            print(f"    ❌ Request exception: {str(e)}")
            return None

    def _test_500_error(self, http_client, app_url):
        """Test 500 error handling"""
        try:
            # 500 errors are harder to trigger as they're usually internal server errors
            # We can try several methods to trigger 500 errors

            # Method 1: Try sending requests that might cause server errors
            # Send oversized POST data
            large_data = "x" * 100000  # 100KB data
            response = http_client.post(f"{app_url}/", data=large_data, allow_redirects=False)
            print(f"    📡 Request: POST / with large data")
            print(f"    📊 Response status: {response.status_code}")

            # If didn't get 500, try other methods
            if response.status_code != 500:
                # Method 2: Try sending complex data that might cause parsing errors
                complex_data = {"key": "value" * 10000}
                response = http_client.post(f"{app_url}/", json=complex_data, allow_redirects=False)
                print(f"    📡 Fallback request: POST / with complex JSON")
                print(f"    📊 Response status: {response.status_code}")

            # If still no 500, try accessing potentially non-existent internal endpoints
            if response.status_code != 500:
                # Method 3: Try accessing paths that might cause internal errors
                response = http_client.get(f"{app_url}/admin/debug/error", allow_redirects=False)
                print(f"    📡 Fallback request: GET /admin/debug/error")
                print(f"    📊 Response status: {response.status_code}")

            return response

        except requests.exceptions.ConnectionError:
            print(f"    ❌ Connection error, please ensure application is running")
            return None
        except Exception as e:
            print(f"    ❌ Request exception: {str(e)}")
            return None
