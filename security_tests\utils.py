"""
Common Test Utility Functions
"""
import re
import os
import sqlite3
import base64
import math
import string
import random
import datetime
import binascii
from urllib.parse import urljoin
from bs4 import BeautifulSoup

def calculate_entropy(text):
    """
    Calculate the entropy value of text to determine randomness
    
    Parameters:
    - text: The text to analyze
    
    Returns:
    - The entropy value of the text
    """
    if not text:
        return 0
    
    # Calculate the frequency of each character
    char_count = {}
    for c in text:
        char_count[c] = char_count.get(c, 0) + 1
    
    # Calculate entropy
    entropy = 0
    text_len = len(text)
    for count in char_count.values():
        freq = count / text_len
        entropy -= freq * math.log2(freq)
    
    return entropy

def calculate_special_char_ratio(text):
    """
    Calculate the ratio of special characters in the text
    
    Parameters:
    - text: The text to analyze
    
    Returns:
    - The ratio of special characters
    """
    if not text:
        return 0
    
    special_chars = sum(1 for c in text if not c.isalnum() and not c.isspace())
    return special_chars / len(text)

def lacks_repetitive_patterns(text, pattern_length=3):
    """
    Check if the text lacks repetitive patterns
    
    Parameters:
    - text: The text to analyze
    - pattern_length: The length of patterns to check
    
    Returns:
    - True if the text lacks repetitive patterns
    """
    if len(text) < pattern_length * 2:
        return False
    
    # Count occurrences of all patterns
    patterns = {}
    for i in range(len(text) - pattern_length + 1):
        pattern = text[i:i+pattern_length]
        patterns[pattern] = patterns.get(pattern, 0) + 1
    
    # Calculate the ratio of repetitive patterns
    repeated_patterns = sum(1 for count in patterns.values() if count > 1)
    unique_patterns = len(patterns)
    
    # If the ratio of repetitive patterns is low, it might be encrypted
    if unique_patterns > 0:
        repeat_ratio = repeated_patterns / unique_patterns
        return repeat_ratio < 0.1  # Empirical threshold
    
    return False

def has_natural_language_features(text):
    """
    Check if the text has natural language features
    
    Parameters:
    - text: The text to analyze
    
    Returns:
    - True if the text has natural language features
    """
    if not text or len(text) < 10:
        return False
    
    # Check for common words
    common_words = ['the', 'a', 'an', 'and', 'or', 'but', 'is', 'are', 'was', 'were', 'in', 'on', 'at', 'to', 'for']
    text_lower = text.lower()
    
    # Check space ratio
    space_ratio = text.count(' ') / len(text)
    
    # Check occurrence of common words
    word_count = 0
    for word in common_words:
        word_count += text_lower.count(' ' + word + ' ')
    
    # If space ratio is reasonable and contains common words, it might be natural language
    return space_ratio > 0.1 and word_count > 0

def has_encryption_characteristics(text):
    """
    Comprehensively check if the text has characteristics of encrypted content
    
    Parameters:
    - text: The text to analyze
    
    Returns:
    - True if the text has encryption characteristics
    """
    if not text:
        return False
    
    # 1. Entropy detection - Encrypted text typically has high entropy
    entropy = calculate_entropy(text)
    if entropy > 4.0:  # Empirical threshold
        return True
    
    # 2. Special character ratio - Encrypted text typically contains many special characters
    special_char_ratio = calculate_special_char_ratio(text)
    if special_char_ratio > 0.3:  # Empirical threshold
        return True
    
    # 3. Repetitive pattern detection - Encrypted text typically lacks repetitive patterns
    if lacks_repetitive_patterns(text):
        return True
    
    # 4. Language feature detection - Encrypted text typically doesn't match natural language features
    if not has_natural_language_features(text):
        # Only consider this factor when the text length is sufficient
        if len(text) > 20:
            return True
    
    # 5. Common encryption format detection
    # Base64
    base64_pattern = r'^[A-Za-z0-9+/]+={0,2}$'
    if re.match(base64_pattern, text.strip()):
        return True
    
    # Hexadecimal
    hex_pattern = r'^[0-9A-Fa-f]+$'
    if re.match(hex_pattern, text.strip()):
        return True
    
    # Fernet format
    if text.startswith('gAAAAA'):
        return True
    
    return False

def compare_frontend_db_content(frontend_content, db_content):
    """
    Compare content displayed in the frontend with content stored in the database
    
    Parameters:
    - frontend_content: Content displayed in the frontend
    - db_content: Content stored in the database
    
    Returns:
    - True if the content differs and the database content has encryption characteristics
    """
    if not frontend_content or not db_content:
        return False
    
    # If the content is identical, there's no encryption
    if frontend_content == db_content:
        return False
    
    # Check if database content has encryption characteristics
    return has_encryption_characteristics(db_content)

def get_post_content_from_frontend(session, app_url, post_id=None, post_title=None):
    """
    Get post content from the frontend
    
    Parameters:
    - session: Session object
    - app_url: Application URL
    - post_id: Post ID (optional)
    - post_title: Post title (optional)
    
    Returns:
    - Dictionary containing post title and content
    """
    try:
        # If no ID or title is specified, try to get the latest post
        if not post_id and not post_title:
            # Visit the posts list page
            response = session.get(f"{app_url}/posts")
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # Find post link
            post_link = None
            for link in soup.find_all('a', href=True):
                href = link['href'].lower()
                if 'post' in href or 'view' in href or 'detail' in href:
                    post_link = link['href']
                    if not post_link.startswith('http'):
                        post_link = urljoin(app_url, post_link)
                    break
            
            if not post_link:
                return None
            
            # Visit post detail page
            response = session.get(post_link)
        elif post_title:
            # Find post using title
            post_link = find_view_post_link(session, app_url, post_title)
            if not post_link:
                return None
            
            response = session.get(post_link)
        else:
            # Construct URL directly using ID (this is a guess, may need to be adjusted based on app URL pattern)
            response = session.get(f"{app_url}/posts/{post_id}")
            
            # If that fails, try other possible URL patterns
            if response.status_code != 200:
                alt_urls = [
                    f"{app_url}/post/{post_id}",
                    f"{app_url}/view/{post_id}",
                    f"{app_url}/posts/view/{post_id}",
                    f"{app_url}/post/view/{post_id}"
                ]
                
                for url in alt_urls:
                    alt_response = session.get(url)
                    if alt_response.status_code == 200:
                        response = alt_response
                        break
        
        # Parse page content
        if response.status_code == 200:
            return extract_post_content(response.text)
        
        return None
    except:
        return None

def get_post_content_from_db(db_connection, post_id=None, post_title=None):
    """
    Get post content from the database
    
    Parameters:
    - db_connection: Database connection
    - post_id: Post ID (optional)
    - post_title: Post title (optional)
    
    Returns:
    - Dictionary containing post title and content
    """
    if not db_connection:
        return None
    
    try:
        cursor = db_connection.cursor()
        
        # Find posts table
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name LIKE '%post%'")
        tables = cursor.fetchall()
        
        if not tables:
            return None
        
        table_name = tables[0][0]
        
        # Find title and content columns
        cursor.execute(f"PRAGMA table_info({table_name})")
        columns = cursor.fetchall()
        
        title_column = None
        body_column = None
        id_column = None
        
        for col in columns:
            col_name = col[1].lower()
            if 'title' in col_name:
                title_column = col[1]
            elif any(term in col_name for term in ['body', 'content', 'text']):
                body_column = col[1]
            elif col_name == 'id' or col_name.endswith('id'):
                id_column = col[1]
        
        if not (title_column and body_column):
            return None
        
        # Build query
        if post_id and id_column:
            cursor.execute(f"SELECT {title_column}, {body_column} FROM {table_name} WHERE {id_column} = ?", (post_id,))
        elif post_title and title_column:
            cursor.execute(f"SELECT {title_column}, {body_column} FROM {table_name} WHERE {title_column} LIKE ?", (f"%{post_title}%",))
        else:
            # Get the latest post
            cursor.execute(f"SELECT {title_column}, {body_column} FROM {table_name} ORDER BY rowid DESC LIMIT 1")
        
        result = cursor.fetchone()
        
        if result:
            return {
                'title': result[0],
                'content': result[1]
            }
        
        return None
    except:
        return None

def is_text_encrypted(text):
    """
    Check if the text appears to be in encrypted form
    This is a heuristic function to determine if text is possibly encrypted
    """
    return has_encryption_characteristics(text)

def is_db_text_encrypted(text):
    """
    Check if text from the database is in encrypted form
    """
    if not text:
        return False
    
    # Use comprehensive detection method
    return has_encryption_characteristics(text)

def get_db_connection(db_path):
    """Create a connection to the SQLite database"""
    return sqlite3.connect(db_path)

def query_db(db_connection, query, args=(), one=False):
    """Execute a database query and return results"""
    if not db_connection:
        return None
    
    try:
        cursor = db_connection.cursor()
        cursor.execute(query, args)
        
        if one:
            return cursor.fetchone()
        return cursor.fetchall()
    except sqlite3.Error:
        return None

def find_all_tables(db_connection):
    """Get all table names in the database"""
    if not db_connection:
        return []
    
    return query_db(db_connection, "SELECT name FROM sqlite_master WHERE type='table'")

def get_table_columns(db_connection, table_name):
    """Get column information for a table"""
    if not db_connection:
        return []
    
    return query_db(db_connection, f"PRAGMA table_info({table_name})")

def check_env_file_exists():
    """Check if the .env file exists"""
    base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    env_path = os.path.join(base_dir, '.env')
    return os.path.isfile(env_path)

def read_env_file():
    """Read the contents of the .env file"""
    base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    env_path = os.path.join(base_dir, '.env')
    if not os.path.isfile(env_path):
        return {}
    
    env_vars = {}
    with open(env_path, 'r') as f:
        for line in f:
            line = line.strip()
            if not line or line.startswith('#'):
                continue
            key, value = line.split('=', 1)
            env_vars[key.strip()] = value.strip()
    
    return env_vars

def check_file_content(file_path, search_patterns, exclude_patterns=None):
    """
    Check if file content matches specified patterns
    
    Parameters:
    - file_path: File path
    - search_patterns: List of regex patterns to search for
    - exclude_patterns: List of regex patterns to exclude (optional)
    
    Returns:
    - List of matching patterns
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            content = file.read()
            
            found_patterns = []
            for pattern in search_patterns:
                if re.search(pattern, content, re.IGNORECASE | re.DOTALL):
                    # If there are exclusion patterns, check if this match should be excluded
                    if exclude_patterns:
                        should_exclude = False
                        for exclude in exclude_patterns:
                            if re.search(exclude, content, re.IGNORECASE | re.DOTALL):
                                should_exclude = True
                                break
                        if not should_exclude:
                            found_patterns.append(pattern)
                    else:
                        found_patterns.append(pattern)
            
            return found_patterns
    except:
        return []

def generate_random_text(length=10):
    """Generate random text for testing"""
    chars = string.ascii_letters + string.digits
    return ''.join(random.choice(chars) for _ in range(length))

def find_post_creation_link(session, app_url):
    """Find the link for creating a post"""
    try:
        # Visit the posts list page
        response = session.get(f"{app_url}/posts")
        
        # Parse the page to find the post creation link
        soup = BeautifulSoup(response.text, 'html.parser')
        create_links = soup.find_all('a', href=True)
        
        for link in create_links:
            href = link['href'].lower()
            text = link.get_text().lower()
            
            if ('create' in href or 'new' in href or 'add' in href) or \
               ('create' in text or 'new' in text or 'add' in text):
                create_url = link['href']
                if not create_url.startswith('http'):
                    create_url = urljoin(app_url, create_url)
                return create_url
        
        return None
    except:
        return None

def create_test_post_in_db(db_connection, post_data, user_id=1):
    """
    Create a test post directly in the database for testing database encryption storage
    
    Note: This is used as a last resort when creating posts through the UI fails
    
    Parameters:
    - db_connection: Database connection
    - post_data: Dictionary containing 'title' and 'content'
    - user_id: User ID
    
    Returns:
    - ID of the successfully created post, or None if failed
    """
    if not db_connection:
        return None
    
    try:
        cursor = db_connection.cursor()
        
        # Find posts table
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name LIKE '%post%'")
        tables = cursor.fetchall()
        
        if not tables:
            return None
        
        table_name = tables[0][0]
        
        # Find title and content columns
        cursor.execute(f"PRAGMA table_info({table_name})")
        columns = cursor.fetchall()
        
        title_column = None
        body_column = None
        userid_column = None
        created_column = None
        
        for col in columns:
            col_name = col[1].lower()
            if 'title' in col_name:
                title_column = col[1]
            elif any(term in col_name for term in ['body', 'content', 'text']):
                body_column = col[1]
            elif any(term in col_name for term in ['user', 'author']):
                userid_column = col[1]
            elif any(term in col_name for term in ['created', 'date', 'time']):
                created_column = col[1]
        
        if not (title_column and body_column):
            return None
        
        # Prepare insert data
        insert_columns = [title_column, body_column]
        insert_values = [post_data['title'], post_data['content']]
        
        if userid_column:
            insert_columns.append(userid_column)
            insert_values.append(user_id)
        
        if created_column:
            insert_columns.append(created_column)
            insert_values.append(datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
        
        # Build insert statement
        columns_str = ', '.join(insert_columns)
        placeholders = ', '.join(['?' for _ in insert_columns])
        
        insert_sql = f"INSERT INTO {table_name} ({columns_str}) VALUES ({placeholders})"
        
        # Execute insert
        cursor.execute(insert_sql, insert_values)
        db_connection.commit()
        
        # Get inserted ID
        cursor.execute("SELECT last_insert_rowid()")
        post_id = cursor.fetchone()[0]
        
        return post_id
    except sqlite3.Error:
        # If an error occurs, rollback and return None
        if db_connection:
            db_connection.rollback()
        return None

def find_view_post_link(session, app_url, post_title=None):
    """Find the link to view a post"""
    try:
        # Visit the posts list page
        response = session.get(f"{app_url}/posts")
        
        # Parse the page to find post links
        soup = BeautifulSoup(response.text, 'html.parser')
        post_links = soup.find_all('a', href=True)
        
        for link in post_links:
            href = link['href'].lower()
            text = link.get_text()
            
            # Find links containing post ID or matching the title
            if ('post' in href or 'view' in href or 'detail' in href) and \
               (not post_title or post_title in text):
                post_url = link['href']
                if not post_url.startswith('http'):
                    post_url = urljoin(app_url, post_url)
                return post_url
        
        return None
    except:
        return None

def extract_post_content(html_content):
    """Extract post content from HTML"""
    soup = BeautifulSoup(html_content, 'html.parser')
    
    # Find elements that might contain post content
    post_content = None
    
    # Find title element
    title_element = soup.find(['h1', 'h2', 'h3'], class_=lambda c: c and ('title' in c or 'post' in c))
    if not title_element:
        title_element = soup.find(['h1', 'h2', 'h3'])
    
    # Find content element
    content_element = soup.find(['div', 'p'], class_=lambda c: c and ('content' in c or 'body' in c or 'text' in c))
    if not content_element:
        # Find content after the post title
        if title_element and title_element.find_next(['div', 'p']):
            content_element = title_element.find_next(['div', 'p'])
    
    # Extract title and content text
    title = title_element.get_text().strip() if title_element else None
    content = content_element.get_text().strip() if content_element else None
    
    return {
        'title': title,
        'content': content
    }

def check_database_encryption(db_connection, post_data):
    """Check if post content is stored encrypted in the database"""
    if not db_connection:
        return False
    
    try:
        cursor = db_connection.cursor()
        
        # Find posts table
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name LIKE '%post%'")
        tables = cursor.fetchall()
        
        if not tables:
            return False
        
        table_name = tables[0][0]
        
        # Find title and content columns
        cursor.execute(f"PRAGMA table_info({table_name})")
        columns = cursor.fetchall()
        
        title_column = None
        body_column = None
        
        for col in columns:
            col_name = col[1].lower()
            if 'title' in col_name:
                title_column = col[1]
            elif any(term in col_name for term in ['body', 'content', 'text']):
                body_column = col[1]
        
        if not (title_column and body_column):
            return False
        
        # Query the latest post records
        cursor.execute(f"SELECT {title_column}, {body_column} FROM {table_name} ORDER BY rowid DESC LIMIT 10")
        posts = cursor.fetchall()
        
        if not posts:
            return False
        
        # Check if at least one post uses encryption
        for post in posts:
            title = post[0]
            content = post[1]
            
            if title and content:
                if is_db_text_encrypted(title) or is_db_text_encrypted(content):
                    return True
        
        return False
    except sqlite3.Error:
        return False

def create_test_post(session, app_url, post_data):
    """
    Attempt to create a test post and return the creation result
    
    Parameters:
    - session: Session object
    - app_url: Application URL
    - post_data: Post data containing title and content
    
    Returns:
    - Creation result dictionary
    """
    # Try to access the post creation page
    try:
        response = session.get(f"{app_url}/posts")
        
        # Parse the page to find the post creation link
        soup = BeautifulSoup(response.text, 'html.parser')
        create_links = soup.find_all('a', href=True)
        
        create_url = None
        for link in create_links:
            if 'create' in link['href'].lower() or 'new' in link['href'].lower() or 'add' in link['href'].lower():
                create_url = link['href']
                if not create_url.startswith('http'):
                    create_url = urljoin(app_url, create_url)
                break
        
        if not create_url:
            return None
        
        # Access the post creation page
        create_response = session.get(create_url)
        create_soup = BeautifulSoup(create_response.text, 'html.parser')
        form = create_soup.find('form')
        
        if not form:
            return None
        
        # Find form fields
        csrf_input = create_soup.find('input', {'name': re.compile(r'csrf.*token', re.I)})
        csrf_token = csrf_input['value'] if csrf_input else None
        
        # Find title and content fields
        title_input = None
        content_input = None
        
        for input_field in create_soup.find_all(['input', 'textarea']):
            name = input_field.get('name', '')
            id_attr = input_field.get('id', '')
            
            if 'title' in name.lower() or 'title' in id_attr.lower():
                title_input = name
            elif any(term in name.lower() or term in id_attr.lower() for term in ['content', 'body', 'text']):
                content_input = name
        
        if not title_input or not content_input:
            return None
        
        # Prepare form data
        form_data = {
            title_input: post_data['title'],
            content_input: post_data['content']
        }
        
        # If there is a CSRF token, add it to form data
        if csrf_token:
            form_data['csrf_token'] = csrf_token
        
        # Get form submission URL
        form_action = form.get('action')
        if not form_action:
            form_action = create_url
        elif not form_action.startswith('http'):
            form_action = urljoin(app_url, form_action)
        
        # Submit the form
        submit_response = session.post(form_action, data=form_data, allow_redirects=True)
        
        # Return creation result
        return {
            'title': post_data['title'],
            'content': post_data['content'],
            'success': 'error' not in submit_response.url.lower(),
            'response': submit_response
        }
    except Exception as e:
        return {
            'title': post_data['title'],
            'content': post_data['content'],
            'success': False,
            'error': str(e)
        }