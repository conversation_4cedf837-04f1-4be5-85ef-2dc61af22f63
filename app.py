from config import app
from flask import render_template


@app.route('/')
def index():
    return render_template('home/index.html')


@app.errorhandler(400)
def bad_request(e):
    return render_template('errors/400.html'), 400


@app.errorhandler(404)
def not_found(e):
    return render_template('errors/404.html'), 404


@app.errorhandler(429)
def too_many_requests(e):
    return render_template('errors/429.html'), 429


@app.errorhandler(500)
def internal_server_error(e):
    return render_template('errors/500.html'), 500


@app.errorhandler(501)
def not_implemented(e):
    return render_template('errors/501.html'), 501





if __name__ == '__main__':
    app.run(ssl_context=('cert.pem', 'key.pem'))
