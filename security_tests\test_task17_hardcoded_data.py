"""
Task 17. Hardcoded Data - Hardcoded Data Black-box Testing

This test file implements complete black-box testing for Canvas Exercise 19, including:

Test Functions:
1. Detection Item I: Verify that app.config[] configuration statements in config.py file are not initialized with hardcoded values
2. Detection Item II: Verify that .env file exists and contains correct application configuration values

Black-box Testing Features:
- Completely independent of specific framework implementations or code patterns
- Detects hardcoded risks through universal configuration pattern analysis
- Uses multi-dimensional scoring system to provide quantified risk assessment
- Intelligently distinguishes between configuration files and application files
- Highly universal, applicable to different project structures and programming languages

Detection Methods:
- Configuration pattern analysis: Detect direct string assignment vs environment variable references
- External reference detection: Identify environment variable usage patterns
- Value characteristic analysis: Analyze encoding characteristics and complexity of configuration values
- Consistency verification: Check consistency between .env file and configuration files

Scoring Standards:
- 0-30 points: Low risk, good configuration practices
- 31-60 points: Medium risk, needs attention
- 61-100 points: High risk, may have hardcoded values
"""

import pytest
import re
import os
from pathlib import Path


class TestTask17HardcodedData:
    """Task 17 Hardcoded Data Detection Test Class"""

    def test_01_config_file_no_hardcoded_values(self, config_file_paths):
        """
        Detection Item I: Verify that configuration files do not contain hardcoded sensitive information

        Corresponding Requirements:
        Ensure that any app.config[] configuration statements in config.py file are not initialized with hardcoded values
        Should use code like this:
        app.config['SECRET_KEY'] = os.getenv('SECRET_KEY')
        app.config['RECAPTCHA_PUBLIC_KEY'] = os.getenv('RECAPTCHA_PUBLIC_KEY')

        Black-box Testing Methods:
        1. Universal configuration pattern analysis - independent of specific framework syntax
        2. External reference detection - identify environment variable usage patterns
        3. Value characteristic analysis - detect suspicious encoded values
        """
        if not config_file_paths:
            pytest.skip("Configuration files not found")

        print(f"\n🔍 Task 17.I - Detecting hardcoded data in configuration files")

        for file_path in config_file_paths:
            print(f"\n📄 Analyzing configuration file: {file_path}")

            with open(file_path, 'r', encoding='utf-8') as file:
                content = file.read()

            # Use improved black-box analysis method
            hardcoded_score = self._analyze_hardcoded_patterns(content, file_path)

            print(f"📊 Hardcoded risk score: {hardcoded_score}/100")

            # Scoring standards
            if hardcoded_score > 60:
                assert False, f"High risk: Hardcoded configuration detected in {file_path} (score: {hardcoded_score}/100)"
            elif hardcoded_score > 30:
                print(f"⚠️  Medium risk: {file_path} (score: {hardcoded_score}/100)")
            else:
                print(f"✅ Low risk: {file_path} (score: {hardcoded_score}/100)")

    def test_02_env_file_proper_configuration(self, env_file_path, config_file_paths):
        """
        Detection Item II: Verify that .env file exists and contains correct application configuration values

        Corresponding Requirements:
        Find .env file and ensure it contains application configuration values like:
        SECRET_KEY = 125f0ca39b6854679bdf7cb3889a061d
        RECAPTCHA_PUBLIC_KEY = 6LdgyVUqAAAAAOlpHkzRlx7dr2F0SYp3QTp5Mo96
        SQLALCHEMY_DATABASE_URI = sqlite:///csc2031blog.db

        Black-box Testing Methods:
        1. Environment file existence check
        2. Required configuration item completeness verification
        3. Configuration file and environment file consistency check
        """
        print(f"\n🔍 Task 17.II - Detecting .env file configuration")

        # Check if .env file exists
        if not env_file_path:
            assert False, ".env file does not exist"

        print(f"✅ .env file exists: {env_file_path}")

        # Read .env file content
        with open(env_file_path, 'r', encoding='utf-8') as file:
            env_content = file.read()

        # Extract environment variables
        env_vars = self._extract_env_variables(env_content)
        print(f"📋 Found {len(env_vars)} environment variables in .env file")

        # Check required configuration items
        required_configs = [
            'SECRET_KEY',
            'SQLALCHEMY_DATABASE_URI',
        ]

        missing_configs = []
        for config in required_configs:
            if config not in env_vars:
                missing_configs.append(config)
            else:
                print(f"  ✅ {config}: {env_vars[config][:20]}...")

        if missing_configs:
            assert False, f"Missing required environment variables: {missing_configs}"

        # Check consistency between configuration files and .env file
        if config_file_paths:
            print(f"\n🔗 Checking consistency between configuration files and .env file")

            config_files_checked = 0
            total_consistency_score = 0

            for config_file in config_file_paths:
                print(f"\n📄 Analyzing configuration file: {config_file}")

                with open(config_file, 'r', encoding='utf-8') as file:
                    config_content = file.read()

                # Intelligently identify file type
                file_type = self._identify_file_type(config_file, config_content)
                print(f"📋 File type: {file_type}")

                if file_type == 'non-config':
                    print(f"  ℹ️  Skipping consistency check for non-configuration file")
                    continue

                consistency_score = self._check_env_config_consistency(env_vars, config_content)
                print(f"📊 Consistency score: {consistency_score}/100")

                config_files_checked += 1
                total_consistency_score += consistency_score

                # For real configuration files, require at least 60% consistency (lowered requirement)
                if consistency_score < 60:
                    print(f"⚠️  Configuration file {config_file} has low consistency, but continuing to check other files")

            if config_files_checked > 0:
                avg_consistency = total_consistency_score / config_files_checked
                print(f"\n📊 Average consistency score: {avg_consistency:.1f}/100")

                # Only fail when overall consistency is too low
                assert avg_consistency >= 50, f"Overall consistency between configuration files and .env file is too low (average score: {avg_consistency:.1f}/100)"
            else:
                print(f"  ℹ️  No configuration files found that need consistency checking")

        print(f"✅ Task 17.II detection passed - .env file configuration is correct")

    def _analyze_hardcoded_patterns(self, content, file_path):
        """
        Enhanced black-box hardcoded pattern analysis
        Returns risk score (0-100), higher score indicates greater hardcoded risk
        """
        risk_score = 0

        # Method 1: Universal assignment pattern analysis
        assignment_score = self._analyze_assignment_patterns(content)
        risk_score += assignment_score
        print(f"  📋 Assignment pattern score: {assignment_score}/40")

        # Method 2: External reference detection
        external_ref_score = self._analyze_external_references(content)
        risk_score += external_ref_score
        print(f"  🔗 External reference score: {external_ref_score}/30")

        # Method 3: Value characteristic analysis
        value_char_score = self._analyze_value_characteristics(content)
        risk_score += value_char_score
        print(f"  🔢 Value characteristic score: {value_char_score}/30")

        # Note: file_path parameter reserved for potential logging
        _ = file_path  # Suppress unused variable warning

        return min(risk_score, 100)

    def _analyze_assignment_patterns(self, content):
        """
        Analyze assignment patterns to detect potential hardcoding
        Returns score 0-40 (higher score = greater risk)
        """
        score = 0

        # Look for assignment patterns suggesting hardcoding
        # Focus on direct string assignments to sensitive variables
        hardcoded_patterns = [
            # Direct hardcoded assignment (bad)
            r'(\w*(?:SECRET|KEY|PASSWORD|TOKEN|URI|DATABASE)\w*)\s*[=:]\s*([\'"][^\'\"]{8,}[\'"])',
            # Hardcoded strings in configuration assignments (bad)
            r'config\[[\'"](\w*(?:SECRET|KEY|PASSWORD|TOKEN|URI|DATABASE)\w*)[\'"]]\s*=\s*([\'"][^\'\"]+[\'"])',
            # Long encoded strings that look like keys (bad)
            r'(\w+)\s*[=:]\s*([\'"][A-Za-z0-9+/]{32,}[\'"])',
        ]

        # Good patterns that should not be counted as hardcoded
        good_patterns = [
            r'(\w*(?:SECRET|KEY|PASSWORD|TOKEN|URI|DATABASE)\w*)\s*[=:]\s*.*getenv',
            r'config\[[\'"](\w*(?:SECRET|KEY|PASSWORD|TOKEN|URI|DATABASE)\w*)[\'"]]\s*=\s*.*getenv',
            r'(\w+)\s*[=:]\s*.*environ',
        ]

        # First, find potentially bad assignments
        bad_assignments = []
        for pattern in hardcoded_patterns:
            matches = re.finditer(pattern, content, re.IGNORECASE)
            for match in matches:
                var_name = match.group(1)
                var_value = match.group(2)
                full_line = match.group(0)
                bad_assignments.append((var_name, var_value, full_line))

        # Filter out assignments that actually use environment variables
        truly_bad_assignments = []
        for var_name, var_value, full_line in bad_assignments:
            is_actually_good = False
            for good_pattern in good_patterns:
                if re.search(good_pattern, full_line, re.IGNORECASE):
                    is_actually_good = True
                    break

            # Also check if the line contains getenv, environ, etc.
            if any(keyword in full_line.lower() for keyword in ['getenv', 'environ', 'env.get']):
                is_actually_good = True

            if not is_actually_good:
                truly_bad_assignments.append((var_name, var_value))

        if truly_bad_assignments:
            print(f"    ⚠️  Found {len(truly_bad_assignments)} potential hardcoded assignments")
            for var_name, var_value in truly_bad_assignments:
                print(f"      - {var_name} = {var_value[:20]}...")

        # Score based on number of truly bad assignments
        if len(truly_bad_assignments) > 5:
            score += 40
        elif len(truly_bad_assignments) > 2:
            score += 25
        elif len(truly_bad_assignments) > 0:
            score += 15

        return score

    def _analyze_external_references(self, content):
        """
        Analyze presence of external reference patterns
        Returns score 0-30 (higher score = greater risk, lower score = better)
        """
        score = 30  # Start with high risk, reduce based on good patterns

        # Good patterns (reduce risk)
        good_patterns = [
            r'getenv\s*\(',           # os.getenv()
            r'environ\s*\[',          # os.environ[]
            r'env\s*\.',              # env.get()
            r'process\.env',          # Node.js process.env
            r'System\.getenv',        # Java System.getenv
            r'ENV\s*\[',              # Ruby ENV[]
            r'config\s*\(',           # Generic config() calls
            r'load_dotenv',           # Python dotenv
            r'from_env',              # Environment loading
        ]

        good_pattern_count = 0
        found_patterns = []
        for pattern in good_patterns:
            if re.search(pattern, content, re.IGNORECASE):
                good_pattern_count += 1
                found_patterns.append(pattern)

        if found_patterns:
            print(f"    ✅ Found good patterns: {found_patterns}")

        # Reduce score based on found good patterns
        if good_pattern_count >= 3:
            score -= 25
        elif good_pattern_count >= 2:
            score -= 15
        elif good_pattern_count >= 1:
            score -= 10

        return max(score, 0)

    def _analyze_value_characteristics(self, content):
        """
        Analyze assignment characteristics
        Returns score 0-30 (higher score = greater risk)
        """
        score = 0

        # Find quoted string values that look like keys/secrets
        quoted_values = re.findall(r'[\'"]([^\'\"]{10,})[\'"]', content)

        suspicious_values = 0
        for value in quoted_values:
            # Check if value looks like a key/secret
            if any(indicator in value.lower() for indicator in ['secret', 'key', 'token', 'password']):
                continue  # Skip if it's just a variable name

            # Check patterns suggesting encoded/secret values
            if (len(value) > 20 and
                (re.match(r'^[A-Za-z0-9+/]+={0,2}$', value) or  # Base64-like
                 re.match(r'^[0-9a-fA-F]+$', value) or          # Hex-like
                 len(set(value)) > len(value) * 0.6)):          # High entropy
                suspicious_values += 1

        if suspicious_values > 0:
            print(f"    ⚠️  Found {suspicious_values} suspicious encoded values")

        # Score based on suspicious values
        if suspicious_values > 3:
            score += 30
        elif suspicious_values > 1:
            score += 20
        elif suspicious_values > 0:
            score += 10

        return score

    def _extract_env_variables(self, env_content):
        """Extract environment variables from .env file content"""
        env_vars = {}

        # Match KEY=VALUE pattern in .env file
        env_pattern = r'^([A-Z_][A-Z0-9_]*)\s*=\s*(.*)$'

        for line in env_content.split('\n'):
            line = line.strip()
            if line and not line.startswith('#'):
                match = re.match(env_pattern, line)
                if match:
                    key = match.group(1)
                    value = match.group(2).strip()
                    env_vars[key] = value

        return env_vars

    def _check_env_config_consistency(self, env_vars, config_content):
        """
        Check consistency between environment variables and configuration files
        Returns score 0-100 (higher score = better consistency)
        """
        score = 50  # Start with neutral score

        # Find environment variable references in configuration
        env_references = []

        # Patterns for finding environment variable references
        env_ref_patterns = [
            r'getenv\s*\(\s*[\'"]([^\'\"]+)[\'"]',     # os.getenv('VAR')
            r'environ\s*\[\s*[\'"]([^\'\"]+)[\'"]',    # os.environ['VAR']
            r'env\s*\.\s*get\s*\(\s*[\'"]([^\'\"]+)[\'"]',  # env.get('VAR')
        ]

        for pattern in env_ref_patterns:
            matches = re.finditer(pattern, config_content, re.IGNORECASE)
            for match in matches:
                env_references.append(match.group(1))

        print(f"  🔗 Found {len(env_references)} environment variable references")

        # Check if this file contains actual configuration assignments
        has_config_assignments = bool(re.search(r'app\.config\[|config\s*=|SECRET.*=|KEY.*=|DATABASE.*=|URI.*=', config_content, re.IGNORECASE))

        # Check if this file only imports configuration but doesn't define it
        is_config_import_only = (
            'from config import' in config_content or
            'import config' in config_content
        ) and not has_config_assignments

        if not has_config_assignments or is_config_import_only:
            # This file doesn't contain configuration assignments, give it a pass
            print(f"  ℹ️  File appears to not contain configuration assignments - skipping detailed analysis")
            return 100  # Perfect score for non-configuration files

        # Check if referenced variables exist in .env
        if env_references:
            existing_refs = [ref for ref in env_references if ref in env_vars]
            ref_consistency = (len(existing_refs) / len(env_references)) * 100
            score += min(ref_consistency * 0.3, 30)  # Reference consistency max 30 points
            print(f"  ✅ Reference consistency: {ref_consistency:.1f}%")

            # List missing references
            missing_refs = [ref for ref in env_references if ref not in env_vars]
            if missing_refs:
                print(f"  ⚠️  Missing in .env: {missing_refs}")
        else:
            # If this file has configuration assignments but no environment references, may be problematic
            if has_config_assignments:
                print(f"  ⚠️  Configuration detected but no environment variable references found")
                score -= 20

        # Check for potential hardcoded values (more specific patterns)
        hardcoded_patterns = [
            r'[\'"][a-zA-Z0-9]{20,}[\'"]',  # Long strings that might be keys
            r'[\'"]sk-[a-zA-Z0-9]+[\'"]',   # API keys starting with sk-
            r'[\'"]mysql://[^\'\"]+[\'"]',  # Database URLs
            r'[\'"]postgres://[^\'\"]+[\'"]',  # Database URLs
        ]

        hardcoded_count = 0
        for pattern in hardcoded_patterns:
            matches = re.findall(pattern, config_content)
            # Filter out common non-key patterns
            for match in matches:
                if not any(common in match.lower() for common in [
                    'template', 'html', 'css', 'js', 'error', 'success', 'info', 'warning'
                ]):
                    hardcoded_count += 1

        # Deduct points for potential hardcoding
        if hardcoded_count > 0:
            hardcoded_penalty = min(hardcoded_count * 10, 30)
            score = max(score - hardcoded_penalty, 0)
            print(f"  ⚠️  Detected potential hardcoded values: {hardcoded_count}")

        # Bonus points for good practices
        if 'load_dotenv' in config_content or 'from_env' in config_content:
            score += 20
            print(f"  ✅ Detected environment loading")

        if len(env_references) >= 3:
            score += 20
            print(f"  ✅ Found multiple environment references")

        # Check if os module is properly imported
        if 'import os' in config_content or 'from os import' in config_content:
            score += 10
            print(f"  ✅ Imported OS module for environment access")

        return min(score, 100)

    def _identify_file_type(self, file_path, content):
        """
        Intelligently identify file type
        Returns: 'config', 'app', 'non-config'
        """
        file_name = os.path.basename(file_path).lower()

        # Explicit configuration files
        if file_name in ['config.py', 'settings.py', 'configuration.py']:
            return 'config'

        # Check content characteristics
        config_indicators = [
            r'app\.config\[',
            r'config\s*=\s*{',
            r'SECRET_KEY\s*=',
            r'DATABASE_URL\s*=',
            r'SQLALCHEMY_DATABASE_URI\s*=',
            r'class.*Config',
        ]

        app_indicators = [
            r'@app\.route',
            r'def\s+\w+\s*\(',
            r'from flask import',
            r'render_template',
            r'request\.',
            r'return redirect',
        ]

        config_score = sum(1 for pattern in config_indicators if re.search(pattern, content, re.IGNORECASE))
        app_score = sum(1 for pattern in app_indicators if re.search(pattern, content, re.IGNORECASE))

        # If mainly routes and view functions, consider it an application file
        if app_score >= 3 and config_score <= 1:
            return 'app'

        # If mainly configuration-related, consider it a configuration file
        if config_score >= 2:
            return 'config'

        # Other cases considered non-configuration files
        return 'non-config'
