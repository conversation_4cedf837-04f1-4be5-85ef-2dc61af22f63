{% extends "base.html" %}

{% block content %}
<div class="container">
    <h1>Login</h1>
    <div class="p-2 row">
        <div class="col-3"></div>
        <div class="col-6">
            <form method="POST">
                <div class="p-2 bg-light border border-primary">
                    <div class="text-left">
                        {% if form %}
                        {{ form.csrf_token() }}
                        {% endif %}
                        <div>
                            {% with messages = get_flashed_messages(with_categories=true) %}
                            {% for category, message in messages %}
                            <div class="alert alert-{{ category }} mt-3 alert-dismissible" role="alert">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"
                                        onclick=delete_flash(this)>
                                    <span>&times;</span>
                                </button>
                            </div>
                            {% endfor %}
                            {% endwith %}
                        </div>
                        {% if form %}
                        <div class="form-group">
                            {{ form.email.label}}<span class="text-danger">*</span>
                            {{ form.email(class="form-control") }}
                        </div>

                        <div class="form-group">
                            {{ form.password.label}}<span class="text-danger">*</span>
                            {{ form.password(class="form-control") }}
                        </div>

                        <div class="form-group">
                            {{ form.pin.label}}<span class="text-danger">*</span>
                            {{ form.pin(class="form-control") }}
                        </div>

                        <div class="form-group">
                           {{ form.recaptcha }}
                        </div>
                        {% endif %}
                    </div>
                    {% if form %}
                    <div>
                        {{ form.submit(class="btn btn-success") }}
                    </div>
                    {% endif %}
                </div>
            </form>

        </div>
        <div class="col-3"></div>
    </div>

</div>
{% endblock %}