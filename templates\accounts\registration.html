{% extends "base.html" %}

{% block content %}
<div class="container">
    <h1>Registration</h1>
    <div class="p-2 row">
        <div class="col-3"></div>
        <div class="col-6">
            <div>
                <p>Please complete the following form to register an account to use the CSC2031 Blog.</p>
            </div>

            <form method="POST">
                <div class="p-2 bg-light border border-primary">
                    <div class="text-left">
                        {{ form.csrf_token() }}
                        <div>
                            {% with messages = get_flashed_messages(with_categories=true) %}
                            {% for category, message in messages %}
                            <div class="alert alert-{{ category }} mt-3 alert-dismissible" role="alert">
                                {{ message }}. Go to <a href="{{ url_for('accounts.login') }}">login page</a>.
                                <button type="button" class="btn-close" data-bs-dismiss="alert"
                                        onclick=delete_flash(this)>
                                    <span>&times;</span>
                                </button>
                            </div>
                            {% endfor %}
                            {% endwith %}
                        </div>

                        <div class="form-group">
                            {{ form.email.label}}<span class="text-danger">*</span>
                            {{ form.email(class="form-control") }}
                            {% for error in form.email.errors %}
                                {{ error }}
                            {% endfor %}
                        </div>

                        <div class="form-group">
                            {{ form.firstname.label}}<span class="text-danger">*</span>
                            {{ form.firstname(class="form-control") }}
                            {% for error in form.firstname.errors %}
                                {{ error }}
                            {% endfor %}
                        </div>

                        <div class="form-group">
                            {{ form.lastname.label}}<span class="text-danger">*</span>
                            {{ form.lastname(class="form-control") }}
                        </div>

                        <div class="form-group">
                            {{ form.phone.label}}<span class="text-danger">*</span>
                            {{ form.phone(class="form-control") }}
                            {% for error in form.phone.errors %}
                                {{ error }}
                            {% endfor %}
                        </div>

                        <div class="form-group">
                            {{ form.password.label}}<span class="text-danger">*</span>
                            {{ form.password(class="form-control") }}
                            {% for error in form.password.errors %}
                                {{ error }}
                            {% endfor %}
                        </div>

                        <div class="form-group">
                            {{ form.confirm_password.label}}<span class="text-danger">*</span>
                            {{ form.confirm_password(class="form-control") }}
                            {% for error in form.confirm_password.errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                    </div>

                    <div>
                        {{ form.submit(class="btn btn-success") }}
                    </div>
                </div>
            </form>

        </div>
        <div class="col-3"></div>
    </div>

</div>
{% endblock %}
