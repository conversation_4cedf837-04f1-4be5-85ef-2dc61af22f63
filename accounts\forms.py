from flask_wtf import <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from wtforms import <PERSON><PERSON><PERSON>, PasswordField, SubmitField
from wtforms.validators import <PERSON>Required, EqualTo, Regexp, Length, Email


class RegistrationForm(FlaskForm):

    email = StringField(validators=[DataRequired(), Email()])
    firstname = StringField(validators=[DataRequired(), Regexp('^[a-zA-Z\-]+$', message='Firstname must contain letters or hyphens (-) only!')])
    lastname = StringField(validators=[DataRequired(), Regexp('^[a-zA-Z\-]+$', message='Lastname must contain letters or hyphens (-) only!')])
    phone = StringField(validators=[DataRequired(),
                                    Regexp('^02\d-\d{8}$|^(011\d|01\d1)-\d{7}$|^01\d{3}-(\d{5}|\d{6})$',
                                           message='Invalid phone number format!')
                                    ])
    password = PasswordField(validators=[DataRequired(),
                                         Length(min=8, max=15,
                                                message='Password must be between 8 and 15 characters long!'),
                                         Regexp('.*[A-Z]', message='Password must contain one upper case letter!'),
                                         Regexp('.*\d', message='Password must contain one digit!'),
                                         Regexp('.*\W', message='Password must contain one special character!')
                                         ])
    confirm_password = PasswordField(
        validators=[DataRequired(), EqualTo('password', message='Both password fields must be equal!')])
    submit = SubmitField()


class LoginForm(FlaskForm):
    email = StringField(validators=[DataRequired()])
    password = PasswordField(validators=[DataRequired()])
    pin = StringField(validators=[DataRequired()])
    recaptcha = RecaptchaField()
    submit = SubmitField()
