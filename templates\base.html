<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>CSC2031</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.2/dist/css/bootstrap.min.css">
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.2/dist/js/bootstrap.bundle.min.js"></script>

</head>
<body>
    <style>
    .table {
        margin-left: auto;
        margin-right: auto;
    }
    </style>

    <nav class="nav nav-pills flex-column flex-sm-row">
        <a class="nav-item nav-link" href="{{ url_for('index') }}">Home</a>
        {% if current_user.is_anonymous %}
            <a class="nav-item nav-link" href="{{ url_for('accounts.registration') }}">Registration</a>
            <a class="nav-item nav-link" href="{{ url_for('accounts.login') }}">Login</a>
        {% endif %}
        {% if current_user.is_authenticated %}
            <a class="nav-item nav-link" href="{{ url_for('accounts.account') }}">Account</a>
            {% if current_user.role == 'end_user' %}
                <a class="nav-item nav-link" href="{{ url_for('posts.posts') }}">View Posts</a>
                <a class="nav-item nav-link" href="{{ url_for('posts.create') }}">Create Post</a>
            {% endif %}
            {% if current_user.role == 'sec_admin' %}
                <a class="nav-item nav-link" href="{{ url_for('security.security') }}">Security</a>
            {% endif %}
            {% if current_user.role == 'db_admin' %}
                <a class="nav-item nav-link" href="url_for('admin.index')">DB Admin</a>
            {% endif %}
            <a class="nav-item nav-link" href="{{ url_for('accounts.logout') }}">Logout</a>
        {% endif %}
    </nav>

    <section class="container-fluid p-3 my-3">
        <div class="container-fluid p-3 my-5 text-center">
            {% block content %}
                <!-- The content of any template extending the base template will be embedded in this block -->
            {% endblock %}
        </div>
    </section>
</body>
</html>