{% extends "base.html" %}

{% block content %}

<div class="container">

    <h1>Security</h1>
    <div class="p-2 row">

        <div class="col-14">
            <div class="p-2 bg-light border border-primary text-left" >
                <div>
                    {% with messages = get_flashed_messages(with_categories=true) %}
                    {% for category, message in messages %}
                    <div class="alert alert-{{ category }} mt-3 alert-dismissible" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" onclick=delete_flash(this)>
                            <span>&times;</span>
                        </button>
                    </div>
                    {% endfor %}
                    {% endwith %}
                </div>

                <h4 class="pt-4">User Registration and Login Event Log</h4>
                <div class="card border border-dark">
                    <table class="table">
                        <thead class="thead-dark">
                        <tr>
                            <th scope="col">Account #</th>
                            <th scope="col">Username</th>
                            <th scope="col">Role</th>
                            <th scope="col">Registered</th>
                            <th scope="col">Latest Login</th>
                            <th scope="col">Previous Login</th>
                            <th scope="col">Latest IP</th>
                            <th scope="col">Previous IP</th>
                        </tr>
                        </thead>
                        <tbody>
                        {% for db_log in db_logs %}
                        <tr>
                            <td>{{ db_log.user.get_id() }}</td>
                            <td>{{ db_log.user.email }}</td>
                            <td>{{ db_log.user.role }}</td>
                            <td>{{ db_log.registered_on.strftime('%H:%M:%S %d-%m-%Y') }}</td>
                            {% if db_log.latest_login %}
                            <td>{{ db_log.latest_login.strftime('%H:%M:%S %d-%m-%Y') }}</td>
                            {% else %}
                            <td>None</td>
                            {% endif %}
                            {% if db_log.previous_login %}
                            <td>{{ db_log.previous_login.strftime('%H:%M:%S %d-%m-%Y') }}</td>
                            {% else %}
                            <td>None</td>
                            {% endif %}
                            <td>{{ db_log.latest_ip }}</td>
                            <td>{{ db_log.previous_ip }}</td>
                        </tr>
                        {% endfor %}
                        </tbody>

                    </table>
                </div>

                <h4 class="pt-4">User Event Log</h4>
                <div class="card border border-dark">
                    <table class="table">
                        <thead class="thead-dark">
                        <tr>
                            <th scope="col">Last 10 Security Log Entries</th>
                        </tr>
                        </thead>
                        <tbody>
                        {% for file_log in file_logs %}
                        <tr>
                            <td>{{ file_log }}</td>
                        </tr>
                        {% endfor %}
                        </tbody>
                    </table>
                </div>

            </div>
        </div>

    </div>
</div>

{% endblock %}