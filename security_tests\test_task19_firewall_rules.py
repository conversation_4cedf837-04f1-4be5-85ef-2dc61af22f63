"""
Task 19. Firewall Rules - Firewall Rules Black-box Testing

This test file implements complete black-box testing for Canvas Exercise 21, including:

Test Functions:
Verify firewall rules protection against the following attacks:
1. SQL injection attack detection and blocking
2. XSS attack detection and blocking
3. Path traversal attack detection and blocking

STRICT TESTING REQUIREMENTS:
- ALL attacks must be successfully detected and blocked
- If ANY attack is not blocked, the entire test fails
- This reflects real-world security requirements where any unblocked attack is a critical vulnerability

Black-box Testing Features:
- Completely independent of specific framework implementations or code patterns
- Verify firewall protection effectiveness through actual HTTP requests
- Precisely test specific URLs and expected results from task requirements
- Verify actual display effects of error pages
- Highly universal, applicable to different web application firewalls

Detection Methods:
- Attack simulation: Send real attack requests to test protection effectiveness
- Response analysis: Check if firewall correctly identifies and blocks attacks
- Error page verification: Confirm display of appropriate security error messages
- Status code checking: Verify correct HTTP status codes are returned

Test Cases (precisely matching task requirements):
- {app_url}/union → SQL injection attack detection (MUST be blocked)
- {app_url}/?filename=etc/drop → SQL injection attack detection (MUST be blocked)
- {app_url}/<script> → XSS attack detection (MUST be blocked)
- {app_url}?filename=%3Ciframe%3E → XSS attack detection (MUST be blocked)
- {app_url}/../../password → Path traversal attack detection (MUST be blocked)
- {app_url}?filename=../etc/password → Path traversal attack detection (MUST be blocked)

Note: {app_url} supports HTTP and HTTPS protocol auto-detection
"""

import pytest
import requests
from bs4 import BeautifulSoup
from urllib.parse import quote


class TestTask19FirewallRules:
    """Task 19 Firewall Rules Detection Test Class"""

    def test_01_sql_injection_protection(self, http_client, app_url):
        """
        Detect SQL injection attack protection

        Corresponding Requirements:
        - Enter URL: {app_url}/union → Redirect to error page stating SQL injection attack attempt detected
        - Enter URL: {app_url}/?filename=etc/drop → Redirect to error page stating SQL injection attack attempt detected

        STRICT REQUIREMENT: ALL SQL injection attacks MUST be blocked. If any attack succeeds, the test fails.

        Black-box Testing Methods:
        1. Send HTTP requests containing SQL injection patterns
        2. Verify if firewall detects the attack
        3. Check if error page contains SQL injection attack notification
        4. Verify request is properly blocked or redirected
        5. Require 100% success rate for all attack patterns
        """
        print(f"\n🔍 Task 19 - SQL injection attack protection test")

        # Specific test cases required by task
        sql_injection_tests = [
            {
                'url_path': '/union',
                'description': 'SQL UNION attack',
                'expected_detection': 'SQL injection'
            },
            {
                'url_path': '/?filename=etc/drop',
                'description': 'SQL DROP attack',
                'expected_detection': 'SQL injection'
            }
        ]

        successful_detections = 0

        for test_case in sql_injection_tests:
            print(f"\n📋 Testing: {test_case['description']}")
            url = f"{app_url}{test_case['url_path']}"
            print(f"📡 Request URL: {url}")

            try:
                response = http_client.get(url, allow_redirects=True)
                print(f"📊 Response status code: {response.status_code}")
                print(f"🔗 Final URL: {response.url}")

                # Check if blocked by firewall
                is_blocked = self._check_attack_blocked(response, 'sql injection')

                if is_blocked:
                    print(f"✅ SQL injection attack successfully detected and blocked")
                    successful_detections += 1
                else:
                    print(f"⚠️  SQL injection attack may not have been detected")
                    self._log_response_details(response)

            except requests.exceptions.ConnectionError:
                print(f"❌ Connection error, please ensure application is running")
            except Exception as e:
                print(f"❌ Request exception: {str(e)}")

        # Require ALL SQL injection attacks to be detected
        total_tests = len(sql_injection_tests)
        assert successful_detections == total_tests, f"SQL injection protection test failed, only {successful_detections}/{total_tests} attacks detected. ALL attacks must be blocked."
        print(f"\n✅ SQL injection protection test passed - ALL {successful_detections}/{total_tests} attacks successfully detected and blocked")

    def test_02_xss_protection(self, http_client, app_url):
        """
        Detect XSS attack protection

        Corresponding Requirements:
        - Enter URL: {app_url}/<script> → Redirect to error page stating XSS attack attempt detected
        - Enter URL: {app_url}?filename=%3Ciframe%3E → Redirect to error page stating XSS attack attempt detected

        STRICT REQUIREMENT: ALL XSS attacks MUST be blocked. If any attack succeeds, the test fails.

        Black-box Testing Methods:
        1. Send HTTP requests containing XSS attack patterns
        2. Verify if firewall detects the attack
        3. Check if error page contains XSS attack notification
        4. Verify malicious scripts are properly blocked or escaped
        5. Require 100% success rate for all attack patterns
        """
        print(f"\n🔍 Task 19 - XSS attack protection test")

        # Specific test cases required by task
        xss_tests = [
            {
                'url_path': '/<script>',
                'description': 'Script tag XSS attack',
                'expected_detection': 'XSS'
            },
            {
                'url_path': '?filename=%3Ciframe%3E',  # URL encoded <iframe>
                'description': 'Iframe tag XSS attack',
                'expected_detection': 'XSS'
            }
        ]

        successful_detections = 0

        for test_case in xss_tests:
            print(f"\n📋 Testing: {test_case['description']}")
            url = f"{app_url}{test_case['url_path']}"
            print(f"📡 Request URL: {url}")

            try:
                response = http_client.get(url, allow_redirects=True)
                print(f"📊 Response status code: {response.status_code}")
                print(f"🔗 Final URL: {response.url}")

                # Check if blocked by firewall
                is_blocked = self._check_attack_blocked(response, 'xss')

                if is_blocked:
                    print(f"✅ XSS attack successfully detected and blocked")
                    successful_detections += 1
                else:
                    print(f"⚠️  XSS attack may not have been detected")
                    self._log_response_details(response)

            except requests.exceptions.ConnectionError:
                print(f"❌ Connection error, please ensure application is running")
            except Exception as e:
                print(f"❌ Request exception: {str(e)}")

        # Require ALL XSS attacks to be detected
        total_tests = len(xss_tests)
        assert successful_detections == total_tests, f"XSS protection test failed, only {successful_detections}/{total_tests} attacks detected. ALL attacks must be blocked."
        print(f"\n✅ XSS protection test passed - ALL {successful_detections}/{total_tests} attacks successfully detected and blocked")

    def test_03_path_traversal_protection(self, http_client, app_url):
        """
        Detect path traversal attack protection

        Corresponding Requirements:
        - Enter URL: {app_url}/../../password → Redirect to error page stating path traversal attack attempt detected, or redirect to {app_url}/password and show resource not found error
        - Enter URL: {app_url}?filename=../etc/password → Redirect to error page stating path traversal attack attempt detected

        STRICT REQUIREMENT: ALL path traversal attacks MUST be blocked. If any attack succeeds, the test fails.

        Black-box Testing Methods:
        1. Send HTTP requests containing path traversal patterns
        2. Verify if firewall detects attack or properly handles paths
        3. Check if access to sensitive files is blocked
        4. Verify error pages or 404 responses
        5. Require 100% success rate for all attack patterns
        """
        print(f"\n🔍 Task 19 - Path traversal attack protection test")

        # Specific test cases required by task
        path_traversal_tests = [
            {
                'url_path': '/../../password',
                'description': 'Directory traversal attack',
                'expected_detection': 'path traversal',
                'alternative_acceptable': 'password'  # Acceptable to redirect to /password and show 404
            },
            {
                'url_path': '?filename=../etc/password',
                'description': 'Parameter path traversal attack',
                'expected_detection': 'path traversal',
                'alternative_acceptable': None
            }
        ]

        successful_detections = 0

        for test_case in path_traversal_tests:
            print(f"\n📋 Testing: {test_case['description']}")
            url = f"{app_url}{test_case['url_path']}"
            print(f"📡 Request URL: {url}")

            try:
                response = http_client.get(url, allow_redirects=True)
                print(f"📊 Response status code: {response.status_code}")
                print(f"🔗 Final URL: {response.url}")

                # Check if blocked by firewall
                is_blocked = self._check_attack_blocked(response, 'path traversal')

                # Check if it's an acceptable alternative result
                is_alternative_acceptable = False
                if test_case.get('alternative_acceptable'):
                    if (test_case['alternative_acceptable'] in response.url and
                        response.status_code == 404):
                        is_alternative_acceptable = True
                        print(f"✅ Path properly handled, redirected to safe path and returned 404")

                if is_blocked or is_alternative_acceptable:
                    print(f"✅ Path traversal attack successfully handled")
                    successful_detections += 1
                else:
                    print(f"⚠️  Path traversal attack may not have been detected")
                    self._log_response_details(response)

            except requests.exceptions.ConnectionError:
                print(f"❌ Connection error, please ensure application is running")
            except Exception as e:
                print(f"❌ Request exception: {str(e)}")

        # Require ALL path traversal attacks to be detected
        total_tests = len(path_traversal_tests)
        assert successful_detections == total_tests, f"Path traversal protection test failed, only {successful_detections}/{total_tests} attacks detected. ALL attacks must be blocked."
        print(f"\n✅ Path traversal protection test passed - ALL {successful_detections}/{total_tests} attacks successfully detected and blocked")

    def _check_attack_blocked(self, response, attack_type):
        """
        Check if attack was blocked by firewall

        Parameters:
        - response: HTTP response object
        - attack_type: Attack type ('sql injection', 'xss', 'path traversal')

        Returns:
        - bool: True if attack was detected and blocked, False if may not have been detected
        """
        # Check status code - 4xx or 5xx usually indicates blocking
        if response.status_code >= 400:
            print(f"    🛡️  Status code {response.status_code} indicates request was blocked")
            return True

        # Parse response content
        try:
            soup = BeautifulSoup(response.text, 'html.parser')
            page_text = soup.get_text().lower()

            # Check corresponding detection keywords based on attack type
            detection_keywords = {
                'sql injection': [
                    'sql injection', 'sql attack', 'injection attack',
                    'malicious sql', 'database attack', 'sql detected'
                ],
                'xss': [
                    'xss', 'cross-site scripting', 'script attack',
                    'malicious script', 'xss attack', 'scripting detected'
                ],
                'path traversal': [
                    'path traversal', 'directory traversal', 'path attack',
                    'traversal attack', 'directory attack', 'path detected'
                ]
            }

            # General security keywords
            general_security_keywords = [
                'security violation', 'attack detected', 'malicious request',
                'security alert', 'blocked request', 'firewall', 'waf',
                'security error', 'threat detected'
            ]

            # Check keywords for specific attack type
            specific_keywords = detection_keywords.get(attack_type, [])
            found_specific = [kw for kw in specific_keywords if kw in page_text]

            # Check general security keywords
            found_general = [kw for kw in general_security_keywords if kw in page_text]

            if found_specific:
                print(f"    🎯 Detected specific attack keywords: {found_specific}")
                return True

            if found_general:
                print(f"    🛡️  Detected general security keywords: {found_general}")
                return True

            # Check if page title contains error or security information
            title = soup.find('title')
            if title:
                title_text = title.get_text().lower()
                if any(kw in title_text for kw in ['error', 'blocked', 'security', 'forbidden']):
                    print(f"    📋 Page title indicates security blocking: {title_text}")
                    return True

            return False

        except Exception as e:
            print(f"    ⚠️  Error parsing response content: {str(e)}")
            return False

    def _log_response_details(self, response):
        """Log response details for debugging"""
        try:
            soup = BeautifulSoup(response.text, 'html.parser')
            page_text = soup.get_text()

            print(f"    📄 Response content preview:")
            print(f"    📏 Content length: {len(response.text)} characters")

            # Show page title
            title = soup.find('title')
            if title:
                print(f"    📋 Page title: {title.get_text().strip()}")

            # Show first 200 characters of text content
            preview_text = page_text.strip()[:200]
            if preview_text:
                print(f"    📝 Content preview: {preview_text}...")

            # Check if contains any error indicators
            error_indicators = ['error', 'exception', 'traceback', 'warning']
            found_errors = [indicator for indicator in error_indicators
                          if indicator in page_text.lower()]
            if found_errors:
                print(f"    ⚠️  Found error indicators: {found_errors}")

        except Exception as e:
            print(f"    ❌ Error logging response details: {str(e)}")
