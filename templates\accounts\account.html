{% extends "base.html" %}

{% block content %}

<div class="container">

    <h1>Account</h1>
    <div class="p-2 row">
        <div class="col-2"></div>
        <div class="col-8">
            <div class="p-2 bg-light border border-primary text-left">
                <div>
                    {% with messages = get_flashed_messages(with_categories=true) %}
                    {% for category, message in messages %}
                    <div class="alert alert-{{ category }} mt-3 alert-dismissible" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" onclick=delete_flash(this)>
                            <span>&times;</span>
                        </button>
                    </div>
                    {% endfor %}
                    {% endwith %}
                </div>

                <div class="card border border-dark">

                    <div class="card-body">
                        <p><b>Account No:</b> {{ current_user.get_id() }}</p>
                        <p><b>Role:</b> {{ current_user.role }}</p>
                        <p><b>Email:</b> {{ current_user.email }}</p>
                        <p><b>First Name:</b> {{ current_user.firstname }}</p>
                        <p><b>Last Name:</b> {{ current_user.lastname }}</p>
                        <p><b>Phone No:</b> {{ current_user.phone }}</p>
                        <p><b>Posts:</b>
                        {% for post in current_user.posts %}
                        {% with decrypted_post = post.decrypt() %}

                        <div class="card border border-dark">
                            <div class="card-header bg-dark text-white border border-dark">
                                <h4>{{ post.title }}</h4>
                                <small>{{ post.created.strftime('%H:%M:%S %d-%m-%Y') }}</small>
                            </div>
                            <div class="card-body">{{ post.body }}</div>
                            <div class="card-footer">
                                <a class="navbar-item" href="{{ url_for('posts.update', id=post.id) }}">Update</a>
                                <a class="navbar-item" href="{{ url_for('posts.delete', id=post.id) }}">Delete</a>
                            </div>
                        </div>
                        <br>
                        {% endwith %}
                        {% endfor %}

                    </div>

                </div>
                <br>

            </div>
        </div>
        <div class="col-2"></div>
    </div>
</div>

{% endblock %}