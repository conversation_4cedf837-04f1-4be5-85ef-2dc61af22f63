# Flask Application Security Features Black-box Testing System Project Report

## 1. Project Overview

This project develops a security black-box testing system for Flask applications, strictly adhering to black-box testing principles. Without conducting source code audits, it verifies the implementation of Flask application security features through HTTP requests, database analysis, behavioral observation, and other technical means. The system corresponds to core security detection projects in the Canvas grading manual, providing automated and standardized detection solutions for Flask application security assessment.

### 1.1 Core Principles of Black-box Testing

1. **Interface-oriented Testing**: Testing only through external interfaces of the application (HTTP endpoints, database query results)
2. **Behavioral Verification**: Inferring internal implementation by observing input-output behavior of the application
3. **Implementation Independence**: Testing logic does not depend on specific function names, variable names, or code structures
4. **Universal Adaptability**: Ability to adapt to different Flask project implementation approaches

### 1.2 System Architecture Design

The system adopts a modular design, with each security feature corresponding to an independent testing module:
- `test_task16_symmetric_encryption.py` - Symmetric encryption detection
- `test_task17_hardcoded_data.py` - Hardcoded data detection
- `test_task18_error_handling.py` - Error handling detection

## 2. Task 16 Symmetric Encryption Detection

### 2.1 Detection Requirements Analysis

According to Canvas grading standards, Task 16 needs to verify the following three key requirements:
1. **Functional Verification**: User <EMAIL> can normally create and update posts, and they display as readable text in the web interface
2. **Database Encryption Verification**: The title and body columns in the posts table store encrypted data (not plaintext)
3. **KDF Implementation Verification**: The encryption implementation uses Key Derivation Function (KDF) rather than hardcoded keys

### 2.2 Core Technical Implementation

#### 2.2.1 reCAPTCHA Test Key Replacement Mechanism
Uses Google's official test key pairs to bypass human verification. The system automatically detects reCAPTCHA configuration in `.env` and `config.py` through filesystem scanning, verifying whether test keys have been correctly replaced.

#### 2.2.2 MFA PIN Code Dynamic Acquisition
**TOTP Key Extraction**: Uses regular expressions to extract 32-bit Base32 encoded TOTP keys from user registration HTTP responses
**Dynamic PIN Generation**: Uses pyotp library to generate 6-digit TOTP codes in real-time based on extracted keys, considering 30-second time window characteristics

#### 2.2.3 Database Encryption State Detection Algorithm
**Multi-dimensional Detection Mechanism**:
- **Encoding Format Recognition**: Base64 pattern and hexadecimal pattern detection
- **Shannon Entropy Analysis**: Calculates information entropy, encrypted text entropy typically >4.0
- **Character Distribution Statistics**: Non-printable character ratio >10% indicates encrypted data
- **Natural Language Feature Exclusion**: Detects space ratio and common English letter distribution

#### 2.2.4 Three-dimensional Strategy for KDF Implementation Verification
**Database Structure Analysis**: Detects existence of `salt` field, KDF-related fields, password field
**Environment Configuration Analysis**: Detects environment variable usage, excludes hardcoded patterns, evaluates configuration complexity
**Behavioral Difference Analysis (Core Innovation)**: Creates two users with same password and content, compares encryption result differences

#### 2.2.5 Adaptive Form Processing
**Semantic Field Recognition**: Automatic mapping based on field name semantics
**CSRF Token Auto-processing**: Automatic detection and extraction of various CSRF token formats
**Multi-path Adaptation**: Supports multiple URL path structures

### 2.3 Scoring Mechanism and Judgment Standards

#### 2.3.1 Scoring System (Total 100 points)
**Functional Testing (30 points)**:
- User Registration and Authentication Process (20 points): reCAPTCHA verification 5 points + User registration 10 points + MFA processing 10 points + Login 5 points
- Post Operation Process (40 points): Post creation 15 points + Post update 10 points + Web interface display 15 points

**Database Encryption Verification (40 points)**:
- Encryption State Detection Algorithm (40 points): Base64 detection 8 points + Hexadecimal detection 8 points + Shannon entropy analysis 12 points + Non-printable characters 8 points + Natural language exclusion 4 points
- Database Verification Requirements (40 points): Title field encryption 20 points + Body field encryption 20 points

**KDF Implementation Verification (30 points)**:
- Database Structure Analysis (10 points): Salt field 5 points + KDF fields 2 points + Password field 2 points + Structure integrity 1 point
- Environment Configuration Analysis (10 points): .env file 2 points + Environment variable usage 3 points + Hardcode exclusion 3 points + Configuration complexity 2 points
- Behavioral Difference Analysis (10 points): Dual user creation 2 points + Same content encryption 3 points + Encryption result difference 4 points + Statistical verification 1 point

#### 2.3.2 Judgment Standards
**Pass Conditions**:
- Total score ≥70 points
- Functional testing ≥20 points
- Encryption verification ≥25 points
- KDF verification ≥15 points

**Failure Conditions**:
- Total score <70 points
- Any dimension below minimum requirements
- Critical failure items: Unable to register/login, plaintext storage, identical encryption results

## 3. Task 17 Hardcoded Data Detection

### 3.1 Detection Requirements Analysis

According to Canvas grading standards, Task 17 needs to verify the following two key requirements:
1. **Configuration File Security Mode Verification**: Ensure that `app.config[]` configuration statements in config.py use `os.getenv()` rather than hardcoded values
2. **Environment Variable Configuration Integrity Verification**: Confirm that .env file exists and contains all necessary application configuration items

### 3.2 Core Technical Implementation

#### 3.2.1 Intelligent Configuration File Recognition Algorithm
**File Type Classifier**: Classifies files into config, app, and non-config categories based on content features
**Classification Algorithm**: Intelligently determines file types through matching scores of configuration indicators and application indicators

#### 3.2.2 Three-dimensional Hardcoded Risk Assessment Algorithm
**Assignment Pattern Analysis (0-40 points)**: Detects direct string assignment patterns, identifies configuration assignment patterns, filters good practices
**External Reference Detection (0-30 points)**: Starts from high risk, decreases scoring based on discovered good patterns
**Value Feature Analysis (0-30 points)**: Detects suspicious encoded values, hexadecimal patterns, high entropy value detection

#### 3.2.3 Environment Variable Consistency Verification Mechanism
**Environment Variable Extraction**: Uses regular expressions to extract key-value pairs from .env files
**Consistency Check Algorithm**: Verifies environment variable references in configuration files
**Scoring Mechanism**: Scores based on reference consistency and good practices

### 3.3 Judgment Standards

**Pass Conditions**:
- Hardcoded risk score ≤ 60 points
- .env file exists and contains required configuration items
- Configuration consistency score ≥ 50 points

**Failure Conditions**:
- Hardcoded risk score > 60 points
- .env file does not exist
- Missing required environment variables
- Configuration consistency score < 50 points

## 4. Task 18 Error Handling Detection

### 4.1 Detection Requirements Analysis

According to Canvas grading standards, Task 18 needs to verify the following two key requirements:
1. **Error Template File Verification**: Ensure that templates or templates/errors folder contains HTML templates for four types of errors (400, 404, 500, 501)
2. **Error Handling Function Verification**: Verify through behavioral testing that each error type has corresponding error handling function implementation

### 4.2 Core Technical Implementation

#### 4.2.1 Error Template File System Detection
**Intelligent Template Discovery Algorithm**: Supports multiple template organization structures
**Template File Recognition Mechanism**: Identifies error codes through filename pattern matching

#### 4.2.2 Template Content Quality Analysis Algorithm
**HTML Structure Verification**: Uses BeautifulSoup to verify HTML integrity
**Error Description Keyword Detection**: Defines specific keyword sets for each error type
**Content Adequacy Verification**: Requires at least 50 characters of descriptive text

#### 4.2.3 Error Handling Behavioral Testing Engine
**Multi-strategy Error Triggering Mechanism**:
- 404 errors: Access non-existent pages
- 400 errors: Send oversized parameters, invalid JSON, wrong HTTP methods
- 501 errors: Use uncommon HTTP methods like PATCH, TRACE, CONNECT
- 500 errors: Send large amounts of data, complex JSON data

#### 4.2.4 Response Verification and Content Analysis
**Flexible Status Code Verification**: Exact matching + similar status code acceptance
**Content Keyword Analysis**: Specific error keywords + general error keywords
**Dual Verification Mechanism**: Status code OR content verification, either passing is acceptable

### 4.3 Judgment Standards

**Pass Conditions**:
- Total score ≥70 points
- Template files ≥35 points
- Behavioral testing ≥30 points (at least 2/4 error handlers working normally)
- All four error templates must exist

**Failure Conditions**:
- Total score <70 points
- Missing required templates
- Template content inadequate
- Fewer than 2 error handling functions working normally

## 5. Task 19 Firewall Rules Detection

### 5.1 Detection Requirements Analysis

According to Canvas grading standards, Task 19 needs to verify firewall protection capabilities against the following three types of attacks:
1. **SQL Injection Attack Protection**: Verify that SQL injection patterns like `/union` and `/?filename=etc/drop` are correctly detected and blocked
2. **XSS Attack Protection**: Verify that XSS attack patterns like `/<script>` and `?filename=%3Ciframe%3E` are correctly detected and blocked
3. **Path Traversal Attack Protection**: Verify that path traversal patterns like `/../../password` and `?filename=../etc/password` are correctly detected and blocked

### 5.2 Core Technical Implementation

#### 5.2.1 Precise Attack Simulation Engine

**SQL Injection Attack Simulation**:
```python
sql_injection_tests = [
    {
        'url_path': '/union',
        'description': 'SQL UNION attack',
        'expected_detection': 'SQL injection'
    },
    {
        'url_path': '/?filename=etc/drop',
        'description': 'SQL DROP attack',
        'expected_detection': 'SQL injection'
    }
]
```

**XSS Attack Simulation**:
```python
xss_tests = [
    {
        'url_path': '/<script>',
        'description': 'Script tag XSS attack',
        'expected_detection': 'XSS'
    },
    {
        'url_path': '?filename=%3Ciframe%3E',  # URL encoded <iframe>
        'description': 'Iframe tag XSS attack',
        'expected_detection': 'XSS'
    }
]
```

**Path Traversal Attack Simulation**:
```python
path_traversal_tests = [
    {
        'url_path': '/../../password',
        'description': 'Directory traversal attack',
        'expected_detection': 'path traversal',
        'alternative_acceptable': 'password'  # Acceptable to redirect to /password and show 404
    },
    {
        'url_path': '?filename=../etc/password',
        'description': 'Parameter path traversal attack',
        'expected_detection': 'path traversal'
    }
]
```

#### 5.2.2 Multi-level Attack Detection Algorithm

**Status Code Detection**:
- 4xx or 5xx status codes indicate requests blocked by firewall
- Exact matching of expected error status codes
- Acceptance of related security error status codes

**Content Keyword Analysis**:
```python
detection_keywords = {
    'sql injection': [
        'sql injection', 'sql attack', 'injection attack',
        'malicious sql', 'database attack', 'sql detected'
    ],
    'xss': [
        'xss', 'cross-site scripting', 'script attack',
        'malicious script', 'xss attack', 'scripting detected'
    ],
    'path traversal': [
        'path traversal', 'directory traversal', 'path attack',
        'traversal attack', 'directory attack', 'path detected'
    ]
}
```

**General Security Keyword Detection**:
```python
general_security_keywords = [
    'security violation', 'attack detected', 'malicious request',
    'security alert', 'blocked request', 'firewall', 'waf',
    'security error', 'threat detected'
]
```

#### 5.2.3 Flexible Verification Mechanism

**Multiple Verification Strategies**:
- **Specific Keyword Verification**: Detects error messages targeting specific attack types
- **General Security Keyword Verification**: Detects general security error prompts
- **Page Title Verification**: Checks security-related information in page titles
- **Status Code Verification**: Verifies HTTP response status codes

**Alternative Result Acceptance Mechanism**:
For path traversal attacks, the system accepts two valid protection results:
1. Direct blocking and displaying path traversal attack detection message
2. Redirecting to safe path (like `/password`) and returning 404 error

#### 5.2.4 Protocol Adaptive Support

**HTTP/HTTPS Auto-detection**:
The system automatically supports HTTP and HTTPS protocols, adjusting test URLs based on actual application deployment to ensure test compatibility and accuracy.

**URL Encoding Processing**:
Correctly handles URL-encoded attack payloads (like `%3Ciframe%3E`), ensuring authenticity and effectiveness of attack simulation.

### 5.3 Judgment Standards

#### 5.3.1 Scoring Mechanism (Total 100 points, All-Pass System)

**SQL Injection Protection (35 points)**:
- `/union` attack detection (20 points): Must successfully detect and block SQL UNION attacks
- `/?filename=etc/drop` attack detection (15 points): Must successfully detect and block SQL DROP attacks
- **Requirement**: Both test cases must pass, otherwise the entire SQL injection protection test fails

**XSS Attack Protection (35 points)**:
- `/<script>` attack detection (20 points): Must successfully detect and block Script tag XSS attacks
- `?filename=%3Ciframe%3E` attack detection (15 points): Must successfully detect and block Iframe tag XSS attacks
- **Requirement**: Both test cases must pass, otherwise the entire XSS protection test fails

**Path Traversal Protection (30 points)**:
- `/../../password` attack detection (15 points): Must successfully detect and block directory traversal attacks or correctly redirect
- `?filename=../etc/password` attack detection (15 points): Must successfully detect and block parameter path traversal attacks
- **Requirement**: Both test cases must pass, otherwise the entire path traversal protection test fails

#### 5.3.2 Judgment Standards

**Pass Conditions (Strict Requirements)**:
- **Total score = 100 points**: All attacks must be successfully defended against
- **All 3 types of attacks detected**: SQL injection, XSS, and path traversal attacks all correctly detected and blocked
- **All test cases for each attack type pass**: Ensures firewall has complete protection capability against various attack variants

**Failure Conditions**:
- **Any attack not blocked**: If any single attack succeeds, the entire test fails
- **Any attack type protection incomplete**: All variants of each attack type must be blocked
- **Firewall not working properly**: Unable to detect any attack patterns

#### 5.3.3 Strict Verification Mechanism

**Detection Method Flexibility**:
- Accepts error messages specific to attack types
- Accepts general security error prompts
- Accepts blocking behavior indicated by status codes

**Alternative Result Recognition**:
- Path traversal attacks can be protected through redirection to safe paths + 404 errors
- Different error page implementations are recognized as long as they contain relevant security prompts

**Strict Requirements**:
- **Zero Tolerance Principle**: Any unblocked attack is considered a serious security vulnerability
- **Complete Protection Requirement**: Firewall must have protection against all known attack patterns
- **Real-world Security Standards**: Reflects firewall security requirements in real environments

### 5.4 Technical Features

**Complete Black-box Verification**: Does not depend on specific firewall implementations, purely verifies protection effectiveness through HTTP behavior
**Precise Canvas Requirements Simulation**: Strictly tests according to specific URLs in Canvas grading standards
**High Compatibility**: Supports different firewall implementations and error page designs
**Intelligent Detection**: Improves attack recognition accuracy through multiple detection methods

## 6. Task 20 Security Headers Detection

### 6.1 Detection Requirements Analysis

According to Canvas grading standards, Task 20 needs to verify the following two key requirements:
1. **Talisman Library Implementation Verification**: Ensure that Talisman library is used to implement security headers in config.py or app.py
2. **CSP Configuration Integrity Verification**: Confirm that Content Security Policy (CSP) is correctly configured to allow execution of reCAPTCHA, Bootstrap CSS and JS

### 6.2 Core Technical Implementation

#### 6.2.1 Hybrid Detection Methodology

**Source Code Inspection (Explicitly Required by Task)**:
Since the Canvas task explicitly requires using the Talisman library, the system uses source code inspection to verify correct import and initialization of the library:
```python
import_patterns = [
    r'from\s+flask_talisman\s+import\s+Talisman',
    r'import\s+flask_talisman',
    r'from\s+flask_talisman\s+import\s+\*'
]

init_patterns = [
    r'Talisman\s*\(\s*app',
    r'talisman\s*=\s*Talisman',
    r'Talisman\s*\(\s*[^)]*app[^)]*\)'
]
```

**Behavioral Verification (Black-box Supplement)**:
Verifies actual working effects of Talisman through HTTP response headers:
```python
talisman_headers = [
    'Content-Security-Policy',
    'X-Content-Type-Options',
    'X-Frame-Options',
    'Strict-Transport-Security'
]
```

#### 6.2.2 Complete Black-box CSP Detection

**HTTP Response Header Analysis**:
The system verifies CSP configuration by analyzing Content-Security-Policy headers in HTTP responses, completely independent of source code:
```python
def _get_csp_header(self, http_client, app_url):
    response = http_client.get(app_url)
    return response.headers.get('Content-Security-Policy')
```

**CSP Directive Parsing Algorithm**:
```python
def _parse_csp_directives(self, csp_header):
    directives = {}
    # Split CSP directives
    directive_parts = [part.strip() for part in csp_header.split(';') if part.strip()]

    for part in directive_parts:
        tokens = part.split()
        if tokens:
            directive_name = tokens[0]
            sources = tokens[1:] if len(tokens) > 1 else []
            directives[directive_name] = sources
```

#### 6.2.3 reCAPTCHA URL Verification Mechanism

**Four-URL Requirement Verification**:
Canvas task requires 4 reCAPTCHA URLs in CSP, the system verifies the following required URLs:
```python
unique_required_urls = [
    'https://www.google.com/recaptcha/',
    'https://www.gstatic.com/recaptcha/',
    'https://recaptcha.google.com/recaptcha/'
]
```

**Multi-directive Checking**:
- **script-src**: Checks JavaScript-related reCAPTCHA URLs
- **frame-src**: Checks iframe-related reCAPTCHA URLs
- **Intelligent Matching**: Supports different URL variants and paths

#### 6.2.4 Bootstrap Resource Verification Algorithm

**CSS Resource Detection**:
Detects Bootstrap CSS resources in style-src directive:
```python
# Detection patterns
if 'bootstrap' in source.lower() and 'css' in source.lower():
    bootstrap_css_found = True
elif 'cdn.jsdelivr.net' in source and 'bootstrap' in source:
    bootstrap_css_found = True
```

**JavaScript Resource Detection**:
Detects Bootstrap JS resources in script-src directive:
```python
# Detection patterns
if 'bootstrap' in source.lower() and 'js' in source.lower():
    bootstrap_js_found = True
elif 'cdn.jsdelivr.net' in source and 'bootstrap' in source:
    bootstrap_js_found = True
```

#### 6.2.5 Intelligent CSP Parsing Technology

**Fault-tolerant Parsing**:
- Supports different CSP formats and whitespace handling
- Intelligently recognizes quoted source addresses
- Handles various CSP directive variants

**Complete Path Verification**:
Ensures Bootstrap resources use complete CDN paths rather than relative or simplified paths

### 6.3 Judgment Standards

#### 6.3.1 Scoring Mechanism (Total 100 points)

**Talisman Library Implementation (50 points)**:
- Source code import check (25 points): Correctly imports flask_talisman library
- Source code initialization check (25 points): Correctly initializes Talisman instance
- Behavioral verification bonus (additional 10 points): HTTP response headers show Talisman working properly

**CSP Configuration Verification (50 points)**:
- reCAPTCHA URL configuration (25 points): Contains required reCAPTCHA domains
- Bootstrap CSS configuration (12.5 points): style-src contains Bootstrap CSS resources
- Bootstrap JS configuration (12.5 points): script-src contains Bootstrap JS resources

#### 6.3.2 Judgment Standards

**Pass Conditions**:
- **Total score ≥70 points**: Comprehensive configuration score reaches above 70 points
- **Talisman library correctly implemented**: Talisman correctly imported and initialized in source code
- **CSP basic configuration complete**: Contains required style-src, script-src, frame-src directives
- **External resources correctly configured**: Both reCAPTCHA and Bootstrap resources correctly configured

**Failure Conditions**:
- **Talisman library not used**: No Talisman import or initialization found in source code
- **CSP configuration missing**: No Content-Security-Policy header found in HTTP response
- **External resource configuration incomplete**: reCAPTCHA or Bootstrap resource configuration missing
- **CSP directives incomplete**: Missing required CSP directives

#### 6.3.3 Technical Features

**Hybrid Detection Method**: Combines source code inspection and black-box behavioral verification to ensure detection accuracy and completeness
**Precise URL Matching**: Strictly verifies specific external resource URLs required by Canvas
**Intelligent Parsing Algorithm**: Supports different CSP formats and configuration methods
**Fault Tolerance Mechanism**: Accepts different CDN paths and URL variants

## 7. Technical Innovations

### 7.1 Multi-user Comparison Verification Method (Task 16)
First use of multi-user comparison in black-box testing to verify correctness of encryption implementation

### 7.2 Three-dimensional Hardcoded Risk Assessment (Task 17)
Innovatively decomposes hardcoded detection into three independent dimensions, providing precise risk assessment

### 7.3 Multi-strategy Error Triggering Mechanism (Task 18)
Developed multiple triggering strategies for different types of HTTP errors, combined with flexible verification mechanisms

### 7.4 Precise Attack Simulation and Multi-level Detection (Task 19)
Developed a testing engine that precisely simulates real attacks, combined with multi-level detection algorithms to ensure accurate assessment of firewall protection effectiveness

### 7.5 Hybrid Detection Methodology (Task 20)
Innovatively combines source code inspection and black-box behavioral verification, maintaining detection universality and accuracy while meeting specific library requirements

## 8. Conclusion

This project successfully implements comprehensive black-box detection of five core security features for Flask applications. Through innovative technical solutions, it addresses key technical challenges including MFA processing, encryption detection, KDF verification, hardcoded data detection, error handling verification, firewall rule testing, and security header configuration verification. The system not only accurately assesses the security implementation level of Flask applications but also provides new technical approaches and methodologies for the black-box security testing field, with significant academic value and practical application significance.
