# Flask Security Features Black-box Testing Tool - User Guide

## Overview

This tool is a specialized black-box testing tool for detecting Flask application security features, corresponding to security detection items in the Canvas grading manual. The tool adopts a complete black-box testing approach, without source code auditing, but verifies security feature implementation through HTTP requests, database analysis, behavioral observation, and other methods.

## Detection Items

This tool detects the following 5 security features:

- **Task 16 - Symmetric Encryption**: Verify symmetric encryption implementation and KDF usage for post content
- **Task 17 - Hardcoded Data**: Detect hardcoded sensitive information in configuration files
- **Task 18 - Error Handling**: Verify custom error pages and error handling functions
- **Task 19 - Firewall Rules**: Test SQL injection, XSS, path traversal attack protection
- **Task 20 - Security Headers**: Detect Talisman library and CSP configuration

## Usage Steps

### Step 0: File Deployment
Place the following files in the Flask project's **runtime directory** (same level as app.py):
- `security_tests/` folder (containing all test files)
- `run_tests.py` file

**Important**: Ensure the database `.db` file has been migrated from the `\instance` folder to the upper runtime directory.

### Step 1: Configure reCAPTCHA Test Keys
To ensure tests can properly pass reCAPTCHA verification, manually find and replace reCAPTCHA keys:

#### Operation Steps:
1. **Find key locations**:
   - Look for lines containing `RECAPTCHA_PUBLIC_KEY` and `RECAPTCHA_PRIVATE_KEY` in the `.env` file
   - Look for lines containing reCAPTCHA public and private key configurations in the `config.py` file

2. **Replace with test keys**:
   - Replace the found **public key value** with: `6LeIxAcTAAAAAJcZVRqyHh71UMIEGNQ_MXjiZKhI`
   - Replace the found **private key value** with: `6LeIxAcTAAAAAGG-vFI1TnRWxMZNFuojJ4WifJWe`

#### Example:
If found in the `.env` file:
```
RECAPTCHA_PUBLIC_KEY=your_original_public_key
RECAPTCHA_PRIVATE_KEY=your_original_private_key
```
Replace with:
```
RECAPTCHA_PUBLIC_KEY=6LeIxAcTAAAAAJcZVRqyHh71UMIEGNQ_MXjiZKhI
RECAPTCHA_PRIVATE_KEY=6LeIxAcTAAAAAGG-vFI1TnRWxMZNFuojJ4WifJWe
```

**⚠️ Important Reminder**: Please backup the original `.env` and `config.py` files before modification!

### Step 2: Save Changes
Manually save the modified `.env` and `config.py` files to ensure changes take effect.

### Step 3: Start Flask Application
Run `app.py` to build the Flask application, ensuring the application runs on local **port 5000**:

```bash
python app.py
```

Verify the application is running normally:
- Visit `http://127.0.0.1:5000` or `https://127.0.0.1:5000`
- Ensure you can access the application homepage normally

### Step 4: Execute Tests
With the Flask application running, execute the test script:

```bash
python run_tests.py
```

## Test Results Description

### Output Format
Test results will display detailed information for each detection item:
- ✅ **Pass**: The security feature is implemented correctly
- ❌ **Fail**: The security feature has issues or is not implemented

### Detection Item Naming Correspondence
Detection item naming directly corresponds to Canvas grading manual scoring items:
- `test_task16_*` → Task 16 symmetric encryption related detection
- `test_task17_*` → Task 17 hardcoded data related detection
- `test_task18_*` → Task 18 error handling related detection
- `test_task19_*` → Task 19 firewall rules related detection
- `test_task20_*` → Task 20 security headers related detection

## Important Notes

### Black-box Testing Features
- This tool adopts a **complete black-box testing** approach
- **Does not perform source code auditing**
- Verifies functionality through HTTP requests, database analysis, behavioral observation, etc.
- Test results are based on actual behavioral performance of the application

### Prerequisites
1. **Flask application must run normally** on local port 5000 (HTTP or HTTPS both acceptable)
2. **Database files must be accessible** (for encryption detection)
3. **reCAPTCHA test keys must be correctly configured** (for form submission testing)

### Troubleshooting
If tests cannot execute normally, please check:

1. **Flask Application Status**:
   - Confirm the application is running
   - Confirm port 5000 is accessible
   - Check console for error messages

2. **File Configuration**:
   - Confirm reCAPTCHA test keys are correctly configured
   - Confirm database file location is correct
   - Confirm `.env` file format is correct

3. **Network Connection**:
   - Confirm local network connection is normal
   - Confirm firewall is not blocking port 5000

### Limitations
- If the Flask application itself cannot start, this detection tool will be ineffective
- In such cases, more detailed manual code review and scoring is required
- The tool only detects implemented features, cannot detect unimplemented security features

## Technical Support

If you encounter problems during use, please check:
1. Flask application runtime status and logs
2. Test tool output information and error messages
3. Network connection and port access status

This tool is designed as an automated detection tool, aimed at improving the efficiency and accuracy of security feature assessment.
