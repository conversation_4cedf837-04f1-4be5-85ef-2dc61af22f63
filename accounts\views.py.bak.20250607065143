from datetime import datetime

from flask import Blueprint, render_template, flash, redirect, url_for, session, request
from flask_login import logout_user, login_user, current_user, login_required
from markupsafe import Markup

from accounts.forms import RegistrationForm, LoginForm
from config import User, db, limiter, logger

accounts_bp = Blueprint('accounts', __name__, template_folder='templates')


@accounts_bp.route('/registration', methods=['GET', 'POST'])
def registration():
    if current_user.is_authenticated:
        flash('You have already registered an account', category="info")
        return redirect(url_for('posts.posts'))

    form = RegistrationForm()

    if form.validate_on_submit():

        if User.query.filter_by(email=form.email.data).first():
            # if this returns a user, then the email already exists in database
            # if email already exists redirect user back to signup page with error message so user can try again
            flash('Email already exists', category="danger")
            return render_template('accounts/registration.html', form=form)

        new_user = User(email=form.email.data,
                        firstname=form.firstname.data,
                        lastname=form.lastname.data,
                        phone=form.phone.data,
                        password=form.password.data,
                        )

        db.session.add(new_user)
        db.session.commit()

        new_user.generate_log()

        logger.warning('[User:{}, Role:{}, IP:{}] Successful Registration'.format(
            new_user.email,
            new_user.role,
            request.remote_addr))

        # flash('Account Created', category='success')
        # return redirect(url_for('accounts.login'))

        flash("Account created. You have to enable Multi-Factor Authentication first to login.", "info")
        return render_template('accounts/setup_mfa.html', uri=new_user.get_mfa_uri(), secret=new_user.mfa_key)

    return render_template('accounts/registration.html', form=form)


@accounts_bp.route('/login', methods=['GET', 'POST'])
@limiter.limit('20 per minute',
               error_message="You have made too many login requests in the last minute")  # 429 error code
def login():

    if current_user.is_authenticated:
        flash('You are already logged in', category="info")
        return redirect(url_for('posts.posts'))

    if not session.get('authentication_attempts'):
        session['authentication_attempts'] = 0

    form = LoginForm()

    if form.validate_on_submit():

        user = User.query.filter_by(email=form.email.data).first()

        if user and user.verify_password(form.password.data):

            if user.verify_mfa(form.pin.data):

                if not user.mfa_enabled:
                    user.mfa_enabled = True
                    db.session.commit()

                session['authentication_attempts'] = 0
                login_user(user)

                user.log.previous_login = user.log.latest_login
                user.log.latest_login = datetime.now()
                user.log.previous_ip = user.log.latest_ip
                user.log.latest_ip = request.remote_addr
                db.session.commit()

                logger.warning('[User:{}, Role:{}, IP:{}] Successful Login'.format(
                    current_user.email,
                    current_user.role,
                    request.remote_addr))
                flash('Login successful', category='success')

                if current_user.role == 'db_admin':
                    return redirect(url_for('admin.index'))
                elif current_user.role == 'sec_admin':
                    return redirect(url_for('security.security'))
                else:
                    return redirect(url_for('posts.posts'))

            elif not user.mfa_enabled:
                flash("You have not enabled Multi-Factor Authentication. Please enable first to login.", "info")
                return render_template('accounts/setup_mfa.html', uri=user.get_mfa_uri(), secret=user.mfa_key)

        session['authentication_attempts'] += 1

    if session['authentication_attempts'] >= 3:

        logger.warning('[User:{}, Attempts:{}, IP:{}] Maximum Invalid Login Attempts'.format(
            form.email.data,
            session['authentication_attempts'],
            request.remote_addr))

        flash(Markup(
            'Number of incorrect login attempts exceeded. Please click <a href = "/reset">here</a> to unlock account.'),
              category="danger")
        return render_template('accounts/login.html')
    elif session['authentication_attempts'] > 0 and session['authentication_attempts'] < 3:

        logger.warning('[User:{}, Attempts:{}, IP:{}] Invalid Login Attempt'.format(
            form.email.data,
            session['authentication_attempts'],
            request.remote_addr))

        flash('Please check your login details and try again, {} login attempts remaining'.format(
            3 - session.get('authentication_attempts')), category="danger")
    return render_template('accounts/login.html', form=form)


# if not session.get('authentication_attempts'):
#     session['authentication_attempts'] = 0
#
# form = LoginForm()
#
# if form.validate_on_submit():
#
#     user = User.query.filter_by(email=form.email.data).first()
#
#     if not user or not user.verify_password(form.password.data) or not user.verify_mfa(form.pin.data):
#
#         session['authentication_attempts'] += 1
#
#         if session.get('authentication_attempts') >= 3:
#             flash(Markup('Number of incorrect login attempts exceeded. Please click <a href = "/reset">here</a> to unlock account.'), category="danger")
#             return render_template('accounts/login.html')
#
#         flash('Please check your login details and try again, {} login attempts remaining'.format(3 - session.get('authentication_attempts')), category="danger")
#         return redirect(url_for('accounts.login'))
#
#     if user and user.verify_password(form.password.data) and not user.verify_mfa(form.pin.data) and not user.mfa_enabled:
#         flash("You have not enabled Multi-Factor Authentication. Please enable first to login.", "info")
#         return render_template('accounts/setup_mfa.html', uri=user.get_mfa_uri(), secret=user.mfa_key)
#
#     session['authentication_attempts'] = 0
#     flash('Login successful', category='success')
#
#     if not user.mfa_enabled:
#         user.mfa_enabled = True
#         db.session.commit()
#
#     return redirect(url_for('posts.posts'))
#
# return render_template('accounts/login.html', form=form)


@accounts_bp.route('/account')
@login_required
def account():
    return render_template('accounts/account.html')


@accounts_bp.route('/reset')
def reset():
    session['authentication_attempts'] = 0
    return redirect(url_for('accounts.login'))


@accounts_bp.route('/logout')
@login_required
def logout():
    logger.warning('[User:{}, Role:{}, IP:{}] Successful Logout'.format(
        current_user.email,
        current_user.role,
        request.remote_addr))

    logout_user()
    return redirect(url_for('index'))
