{% extends "base.html" %}

{% block content %}

<div class="container">

    <h1>Posts</h1>
    <div class="p-2 row">
        <div class="col-2"></div>
        <div class="col-8">
            <div class="p-2 bg-light border border-primary text-left">
                <div>
                    {% with messages = get_flashed_messages(with_categories=true) %}
                        {% for category, message in messages %}
                            <div  class="alert alert-{{ category }} mt-3 alert-dismissible" role="alert">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert" onclick=delete_flash(this)>
                                    <span>&times;</span>
                                </button>
                            </div>
                        {% endfor %}
                    {% endwith %}
                </div>

                {% for post in posts %}
                <div class="card border border-dark">
                    <div class="card-header bg-dark text-white border border-dark">
                        <h4>{{ post.title }}</h4>
                        <p>Author: {{ post.user.firstname }} {{ post.user.lastname }}</p>
                        <small>{{ post.created.strftime('%H:%M:%S %d-%m-%Y') }}</small>
                    </div>
                    {# {% autoescape false %} #}
                    <div class="card-body">{{ post.body }}</div>
                    {# {% endautoescape %} #}
                    <div class="card-footer">
                        {% if current_user.get_id() == post.user.get_id() %}
                            <a class="navbar-item" href="{{ url_for('posts.update', id=post.id) }}">Update</a>
                            <a class="navbar-item" href="{{ url_for('posts.delete', id=post.id) }}">Delete</a>
                        {% endif %}
                    </div>
                </div>
                <br>
                {% endfor %}

            </div>
        </div>
        <div class="col-2"></div>
    </div>
</div>

{% endblock %}