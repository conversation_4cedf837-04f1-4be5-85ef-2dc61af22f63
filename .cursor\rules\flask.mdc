---
description: 
globs: 
alwaysApply: true
---
You are a specialized security testing expert tasked with creating black-box security tests for Flask applications. I need you to generate Python test code that will evaluate a Flask project's security implementation without depending on specific function or variable names.

## Project Context
I have a Flask web application that requires security testing according to specific requirements. The tests must be implementation-agnostic (black-box testing) to work across different implementations that meet the same security requirements.

## Test Requirements
Generate test files for the following security features:
1. Symmetric Encryption for blog posts
2. Environment-based configuration (no hardcoded secrets)
3. Custom error handling pages (400, 404, 500, 501)
4. Firewall rules against common attacks
5. Security headers implementation

## Testing Constraints
- Tests must be BLACK-BOX and not depend on specific function/variable names
- Tests should verify BEHAVIOR and OUTCOMES, not implementation details
- Tests must be placed in a "/pytest" directory
- No modifications to the original project code are allowed
- Tests should be adaptable to work on similar Flask projects

## Output Format
For each security requirement, provide:
1. A test file with clear test functions
2. Comments explaining the testing approach
3. Assertions that verify the security feature works correctly

Also include a "todolist.md" file tracking completed and planned tests.

{{additional_specifications}}


# Flask项目安全测试需求

你需要根据我给你的flask项目写一个pytest程序，放在/security_tests文件夹里面。

## 1. 检测项目要求

### 检测项16. 对称加密：
**I.** 建一个名为*************的用户，并且使用*************登录。发送一个新的帖子，确保新帖子在查看帖子页面上以可读文本显示。更新Bob的帖子，确保更新的文本在查看帖子页面上可读

**II.** 数据库中，确保存在posts表，确保表中title和body列中的值是加密的（不是明文可读文本）

**III.** 打开config.py文件，查看用于加密/解密帖子标题和正文文本的encrypt和decrypt函数，函数名可能不是encrypt或者decrypt，但要确保有类似功能的函数。确保这些函数没有使用存储的/硬编码的加密密钥。加密密钥应该在运行时使用密钥派生函数（KDF）生成。查找名为generate_key或类似的KDF函数（函数名不一定是generate_key，只要有类似的KDF都算正确）。代码应该类似于（不要使用正则表达式检查，只要功能正确即可）：

def generate_key(self):
    key = scrypt(
        password=self.password.encode(), 
        salt=self.salt.encode(),
        n=2048, 
        r=8, 
        p=1, 
        dklen=32 
    )
    return base64.b64encode(key)

### 检测项17. 硬编码数据：
**I.** config.py文件中，确保任何应用配置语句app.config[]都没有使用硬编码值初始化（应该在文件顶部附近）。查找类似以下的代码：

app.config['SECRET_KEY'] = os.getenv('SECRET_KEY')
app.config['RECAPTCHA_PUBLIC_KEY'] = os.getenv('RECAPTCHA_PUBLIC_KEY')
app.config['RECAPTCHA_PRIVATE_KEY'] = os.getenv('RECAPTCHA_PRIVATE_KEY')
app.config['SQLALCHEMY_DATABASE_URI'] = os.getenv('SQLALCHEMY_DATABASE_URI')
app.config['SQLALCHEMY_ECHO'] = os.getenv('SQLALCHEMY_ECHO') == 'True'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = os.getenv('SQLALCHEMY_TRACK_MODIFICATIONS') == 'True'

**II.** 查找.env文件并打开它。它应该包含类似以下的应用配置值（密钥值可能不同）：

SECRET_KEY = 125f0ca39b6854679bdf7cb3889a061d
RECAPTCHA_PUBLIC_KEY = 6LdgyVUqAAAAAOlpHkzRlx7dr2F0SYp3QTp5Mo96
RECAPTCHA_PRIVATE_KEY = 6LdgyVUqAAAAANmq8UrWlHqa4taLr7ZR8nJWh_Pd
SQLALCHEMY_DATABASE_URI = sqlite:///csc2031blog.db
SQLALCHEMY_ECHO = True
SQLALCHEMY_TRACK_MODIFICATIONS = False

### 检测项18. 错误处理：
**I.** 打开templates或templates/errors文件夹。该文件夹应包含以下错误的HTML错误页面/模板：
• 错误请求 (400)
• 未找到 (404)
• 内部服务器错误 (500)
• 未实现 (501)
HTML错误页面模板应包含错误名称，以及一到两段解释错误含义的文字。只要满足上诉的四个模板，即判定为通过。

**II.** 打开app.py或config.py文件，确保为上述列出的每个错误都有错误处理函数。查找类似以下错误请求错误(400)示例的代码：

@app.errorhandler(400)
def bad_request(e):
    return render_template('errors/400.html'), 400

### 检测项19. 防火墙规则：（https和http中只要有一个能满足即可）

Enter URL: https://127.0.0.1:5000/union 
Redirected to error page stating SQL injection attack attempt detected

Enter URL: https://127.0.0.1:5000/?filename=etc/drop
Redirected to error page stating SQL injection attack attempt detected

Enter URL: https://127.0.0.1:5000/<script>
Redirected to error page stating XXS attack attempt detected

Enter URL: https://127.0.0.1:5000?filename=%3Ciframe%3E
Redirected to error page stating XXS attack attempt detected

Enter URL: https://127.0.0.1:5000/../../password
Redirected to error page stating path traversal attack attempt detected Or redirected to https://127.0.0.1:5000/password and shown resource not found error

Enter URL: https://127.0.0.1:5000?filename=../etc/password
Redirected to error page stating path traversal attack attempt detected

### 检测项20. 安全头：
在此任务中，要求学生实现安全头，这些头部阻止导入到应用程序中的外部内容/功能，如reCAPTCHA、HTML样式（CSS库）和JavaScript（JS库）。学生应该实现一个内容安全策略，定义允许的外部内容/功能。

**I.** 在PyCharm中，打开config.py或app.py文件，确保使用Talisman库实现了安全头。查找类似以下的代码，注意这里只允许使用Talisman库：

talisman = Talisman(app, content_security_policy=csp)

**II.** 在config.py或app.py文件中，确保定义了内容安全策略(csp)，允许执行reCAPTCHA、bootstrap CSS和bootstrap JS的外部库。查找类似以下的代码（应使用JS和CSS库的完整路径，并应看到reCAPTCHA的4个URL）：

csp = {
    'style-src': ['\'self\'',
        'https://cdn.jsdelivr.net/npm/bootstrap@4.6.2/dist/css/bootstrap.min.css',
    ],
    'script-src': ['\'self\'',
        'https://www.google.com/recaptcha/',
        'https://www.gstatic.com/recaptcha/',
        'https://cdn.jsdelivr.net/npm/bootstrap@5.2.2/dist/js/bootstrap.bundle.min.js'
    ],
    'frame-src': ['\'self\'',
        'https://www.google.com/recaptcha/',
        'https://recaptcha.google.com/recaptcha/'
    ]
}

## 2. 测试要求

该测试程序必须具有普遍性，且是黑盒测试，这意味着，绝对不能直接在测试程序中使用项目内的特定函数名称或者变量名称等等，因为这些名称在其他的项目内可能不一样，导致无法正确检测。

## 3. 通用性要求

你必须想办法让测试在多个类似的项目内都能正确按照要求检测，这有点像老师批改不同学生的代码作业，他们的实现方法可能不一样，但只要结果正确，他们就应该能得分。你需要充当老师，来判断在给定的规范下他们的作业是否满足要求。

## 4. 限制条件

绝对不能改动项目的源代码，你只能写入测试程序，不可以已一切原因改变被测试项目的结构或者代码。

## 5. 文档更新

每次回答结束后更新todolist.md保存在项目根目录，如果文件不存在，创建一个并写入未来的计划和已经实现的内容。

## 6. 测试框架选择

由于不能使用原本项目内的函数或者变量名称，这导致了诸多不变，你可以选择其他的测试框架，如果你认为pytest不适合这种类型的黑盒测试，请你使用其他的测试框架完成测试。

## 检查要求

检查一下目前的测试是否符合测试要求。特别是项目的普适性和黑盒性，即：不能使用源代码内的函数名和参数名。

**注意：请你严格按照我给你发的要求来写测试，不要加入额外的拓展要求。**

