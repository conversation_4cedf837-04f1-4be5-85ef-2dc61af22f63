{% extends "base.html" %}

{% block content %}

<div class="container">


    <h1>Create Post</h1>
    <div class="p-2 row">
        <div class="col-2"></div>
        <div class="col-8">
            <form method="POST">
                <div class="p-2 bg-light border border-primary text-left">

                    {{ form.csrf_token() }}
                    <div class="form-group">
                        {{ form.title.label}}<span class="text-danger">*</span>
                        {{ form.title(class="form-control") }}
                    </div>
                    <div class="form-group">
                        {{ form.body.label}}<span class="text-danger">*</span>
                        {{ form.body(class="form-control", rows='10') }}

                    </div>
                    <div>
                        {{ form.submit(class="btn btn-success") }}
                    </div>
                </div>
            </form>
        </div>
        <div class="col-2"></div>

    </div>
</div>


{% endblock %}


<!--<div class="box">
<form method="POST">
    <div class="field">
        {{ form.csrf_token() }}
        <div class="control">
            {{ form.title(class="input", placeholder="Title") }}
            {% for error in form.title.errors %}
                {{ error }}
            {% endfor %}
        </div>
    </div>
    <div class="field">
        <div class="control">
            {{ form.body(class="textarea", placeholder="Body") }}
            {% for error in form.body.errors %}
                {{ error }}
            {% endfor %}
        </div>
    </div>
    <div>
        {{ form.submit(class="button is-info is-centered") }}
    </div>
</form>
</div>-->
