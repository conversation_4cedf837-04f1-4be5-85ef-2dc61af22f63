from flask import Blueprint, render_template
from flask_login import login_required

from config import roles_required, Log

security_bp = Blueprint('security', __name__, template_folder='templates')


@security_bp.route('/security')
@login_required
@roles_required('sec_admin')
def security():
    db_logs = Log.query.all()

    with open("security.log", "r") as f:
        file_logs = f.read().splitlines()[-10:]
        file_logs.reverse()

    return render_template('security/security.html', db_logs=db_logs, file_logs=file_logs)
