{% extends "base.html" %}

{% block content %}
<div class="container">
    <h1>Multi-Factor Authentication Setup</h1>
    <div class="p-2 row">
        <div class="col-2"></div>

        <div class="col-8">
            <div>
                {% with messages = get_flashed_messages(with_categories=true) %}
                {% for category, message in messages %}
                <div class="alert alert-{{ category }} mt-3 alert-dismissible" role="alert">
                    {{ message }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"
                            onclick=delete_flash(this)>
                        <span>&times;</span>
                    </button>
                </div>
                {% endfor %}
                {% endwith %}
            </div>
            <ul class="list-group">
                <li class="list-group-item">Open Microsoft Authenticator on your mobile.</li>
                <li class="list-group-item">Alternatively, download <a
                        href="https://play.google.com/store/apps/details?id=com.google.android.apps.authenticator2&hl=en&gl=US"
                        target="_blank">Google Authenticator</a> on your mobile.
                </li>
                <li class="list-group-item">Select add new account.</li>
                <li class="list-group-item">Manually enter Code or scan QR below.</li>
            </ul>
            {% if uri %}
            <div class="m-2 p-2 bg-light border border-primary">
                <h4>Code</h4>
                <p>{{ secret }}</p>
                <h4>QR</h4>
                <p><img src="{{ qrcode(uri, box_size=5, border=2) }}"></p>
            </div>
            {% endif %}
             <ul class="list-group">
                 <li class="list-group-item">Once you have entered the Code or scanned the QR, please click <a href="{{ url_for('accounts.login') }}">here.</a></li>
             </ul>
        </div>

        <div class="col-2"></div>

    </div>
</div>
{% endblock %}