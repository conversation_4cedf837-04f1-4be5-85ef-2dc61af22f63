"""
Global test configuration and common fixtures
"""
import os
import sys
import pytest
import requests
from pathlib import Path
from flask import Flask
from bs4 import BeautifulSoup
import sqlite3
import re
import time
import random
import string
import datetime
from urllib.parse import urljoin
from urllib3.exceptions import InsecureRequestWarning

# Add project root directory to Python path to ensure imports work
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

def pytest_addoption(parser):
    """Add command line options"""
    parser.addoption(
        "--url", action="store", default="auto",
        help="URL of the Flask application (default: auto-detect HTTP/HTTPS)"
    )
    parser.addoption(
        "--skip-connection-check", action="store_true", default=False,
        help="Skip connection check, attempt to run tests even if server is not available"
    )

@pytest.fixture(scope="session")
def app_url(request):
    """
    Return the base URL of the application, supporting both HTTP and HTTPS

    Priority:
    1. Get from FLASK_TEST_URL environment variable
    2. Get from --url command line parameter
    3. Auto-detect protocol if 'auto' is specified
    4. Default to auto-detected protocol (HTTPS preferred)
    """
    # First check environment variable
    url = os.environ.get("FLASK_TEST_URL")

    # If environment variable is not set, get from command line parameter
    if not url:
        url = request.config.getoption("--url")

    # If URL is 'auto', try to auto-detect
    if url == 'auto':
        url = _auto_detect_app_url()

    # Ensure URL doesn't have trailing slash
    if url and url.endswith('/'):
        url = url[:-1]

    return url

def _auto_detect_app_url():
    """Auto-detect the application URL by trying different protocols"""
    import requests
    from urllib3.exceptions import InsecureRequestWarning

    # Disable SSL warnings for auto-detection
    requests.packages.urllib3.disable_warnings(InsecureRequestWarning)

    test_urls = [
        'https://127.0.0.1:5000',
        'http://127.0.0.1:5000',
        'https://localhost:5000',
        'http://localhost:5000'
    ]

    for url in test_urls:
        try:
            response = requests.get(url, verify=False, timeout=2)
            if response.status_code < 500:
                return url
        except:
            continue

    # If auto-detection fails, return default HTTPS
    return 'https://127.0.0.1:5000'

@pytest.fixture(scope="session")
def http_client():
    """Create a session object that ignores SSL errors for testing"""
    session = requests.Session()
    session.verify = False
    # Disable InsecureRequestWarning
    requests.packages.urllib3.disable_warnings(InsecureRequestWarning)
    return session

@pytest.fixture(scope="session", autouse=True)
def check_server_availability(http_client, app_url, request):
    """Check if the application server is available"""
    # If skip connection check is specified, skip this check
    if request.config.getoption("--skip-connection-check"):
        return
    
    try:
        # Try to connect to the application server
        response = http_client.get(app_url, timeout=5)
        if response.status_code >= 400:
            pytest.skip(f"Application server returned error status code: {response.status_code}")
    except requests.RequestException as e:
        pytest.skip(f"Unable to connect to application server ({app_url}): {str(e)}\n"
                   "Please ensure the application is running, or use the --skip-connection-check option to skip this check")

@pytest.fixture(scope="session")
def db_file_path():
    """Return the database file path"""
    # Find SQLite database file in the project
    root_dir = Path(__file__).parent.parent
    instance_dir = root_dir / 'instance'
    if instance_dir.exists():
        for file in instance_dir.glob('*.db'):
            return str(file)
    
    # If not found in instance directory, try to find in root directory
    for file in root_dir.glob('*.db'):
        return str(file)
    
    # If still not found, return None
    return None

@pytest.fixture(scope="function")
def db_connection(db_file_path):
    """Create database connection"""
    if not db_file_path:
        pytest.skip("Database file not found")
    
    try:
        conn = sqlite3.connect(db_file_path)
        conn.row_factory = sqlite3.Row
        yield conn
        conn.close()
    except sqlite3.Error as e:
        pytest.skip(f"Error connecting to database: {str(e)}")

@pytest.fixture(scope="session")
def user_credentials():
    """
    Return test user credentials
    
    Default values can be overridden through environment variables:
    - FLASK_TEST_USER: Test user email
    - FLASK_TEST_PASS: Test user password
    """
    return {
        "email": os.environ.get("FLASK_TEST_USER", "<EMAIL>"),
        "password": os.environ.get("FLASK_TEST_PASS", "Password1!")
    }

@pytest.fixture(scope="function")
def login_session(http_client, app_url, user_credentials):
    """Create a logged-in session, enhanced version supporting various login pages and form structures"""
    try:
        # Visit login page to get CSRF token and form structure
        login_page = http_client.get(f"{app_url}/login", timeout=5)
        
        # If non-200 status code is returned, try other possible login page paths
        if login_page.status_code != 200:
            alt_paths = ['/accounts/login', '/auth/login', '/users/login', '/signin']
            for path in alt_paths:
                try:
                    alt_login_page = http_client.get(f"{app_url}{path}", timeout=5)
                    if alt_login_page.status_code == 200:
                        login_page = alt_login_page
                        break
                except:
                    continue
                    
        # If still unable to access login page, return original client and set flag
        if login_page.status_code != 200:
            http_client.is_logged_in = False
            http_client.login_error = "Unable to access login page"
            return http_client
            
        soup = BeautifulSoup(login_page.text, 'html.parser')
        
        # Find login form
        form = soup.find('form')
        if not form:
            http_client.is_logged_in = False
            http_client.login_error = "Login form not found"
            return http_client
        
        # Find CSRF token
        csrf_token = None
        csrf_input = soup.find('input', {'name': re.compile(r'csrf.*token', re.I)})
        if csrf_input:
            csrf_token = csrf_input.get('value')
        
        # Find username/email and password fields
        email_field = None
        password_field = None
        
        # Find possible username/email input fields
        for input_field in form.find_all('input'):
            input_type = input_field.get('type', '').lower()
            input_name = input_field.get('name', '').lower()
            input_id = input_field.get('id', '').lower()
            
            # Find email/username field
            if (input_type == 'email' or input_type == 'text') and any(term in input_name or term in input_id for term in ['email', 'username', 'user', 'login']):
                email_field = input_field.get('name')
            
            # Find password field
            elif input_type == 'password':
                password_field = input_field.get('name')
        
        # If necessary fields cannot be found, return original client
        if not email_field or not password_field:
            http_client.is_logged_in = False
            http_client.login_error = "Email or password field not found"
            return http_client
        
        # Prepare login data
        login_data = {
            email_field: user_credentials['email'],
            password_field: user_credentials['password']
        }
        
        # Add CSRF token (if it exists)
        if csrf_token:
            login_data['csrf_token'] = csrf_token
        
        # Find submit button and add to form data (if needed)
        submit_button = form.find('button', {'type': 'submit'}) or form.find('input', {'type': 'submit'})
        if submit_button and submit_button.get('name'):
            login_data[submit_button.get('name')] = submit_button.get('value', 'Login')
        
        # Get form submission URL
        form_action = form.get('action')
        if not form_action:
            form_action = login_page.url
        elif not form_action.startswith('http'):
            form_action = urljoin(login_page.url, form_action)
        
        # Submit login form
        login_response = http_client.post(form_action, data=login_data, allow_redirects=True, timeout=5)
        
        # Try to confirm if login was successful
        is_logged_in = 'login' not in login_response.url.lower() and 'signin' not in login_response.url.lower()
        
        # If there might be a captcha, record but continue to try
        recaptcha_field = soup.find('div', {'class': 'g-recaptcha'}) or soup.find('div', {'data-sitekey': True})
        if recaptcha_field:
            # We will note that a captcha is needed, but still return the client to allow test attempts to execute
            http_client.has_recaptcha = True
        
        # Save login status for test function check
        http_client.is_logged_in = is_logged_in
        if not is_logged_in:
            http_client.login_error = "Login failed, possibly due to incorrect username/password or captcha needed"
        
        return http_client
    except requests.RequestException as e:
        # Connection error occurred, return original client
        http_client.is_logged_in = False
        http_client.login_error = str(e)
        return http_client

@pytest.fixture(scope="function")
def authenticated_client(login_session):
    """
    Provide a logged-in client
    
    This is an alias for login_session, for backward compatibility
    """
    return login_session

@pytest.fixture(scope="session")
def env_file_path():
    """Return the path to the .env file"""
    root_dir = Path(__file__).parent.parent
    env_file = root_dir / '.env'
    if env_file.exists():
        return str(env_file)
    return None

@pytest.fixture(scope="session")
def config_file_paths():
    """Return the list of paths to configuration files"""
    root_dir = Path(__file__).parent.parent
    config_files = []
    for file in root_dir.glob('*.py'):
        if file.name in ['config.py', 'app.py']:
            config_files.append(str(file))
    return config_files

@pytest.fixture(scope="session")
def error_template_paths():
    """Return the list of paths to error template files"""
    root_dir = Path(__file__).parent.parent
    templates_dir = root_dir / 'templates'
    
    error_templates = []
    if templates_dir.exists():
        # Check templates/errors directory
        errors_dir = templates_dir / 'errors'
        if errors_dir.exists():
            for file in errors_dir.glob('*.html'):
                error_templates.append(str(file))
        
        # Also check directly in templates directory for error templates
        for file in templates_dir.glob('*.html'):
            if any(error_code in file.name for error_code in ['400', '404', '500', '501']):
                error_templates.append(str(file))
    
    return error_templates

def generate_random_text(length=10):
    """Generate random text for testing"""
    chars = string.ascii_letters + string.digits
    return ''.join(random.choice(chars) for _ in range(length))

@pytest.fixture(scope="function")
def test_post_data():
    """Generate test post data"""
    # Generate random text as test data
    test_title = f"Test Title {generate_random_text()}"
    test_content = f"Test Content {generate_random_text(30)}"
    
    return {
        'title': test_title,
        'content': test_content
    }

def create_test_post(session, app_url, post_data):
    """Try to create a test post and return creation result"""
    # Try to access create post page
    try:
        response = session.get(f"{app_url}/posts")
        
        # Parse page to find create post link
        soup = BeautifulSoup(response.text, 'html.parser')
        create_links = soup.find_all('a', href=True)
        
        create_url = None
        for link in create_links:
            if 'create' in link['href'].lower() or 'new' in link['href'].lower() or 'add' in link['href'].lower():
                create_url = link['href']
                if not create_url.startswith('http'):
                    create_url = urljoin(app_url, create_url)
                break
        
        if not create_url:
            return None
        
        # Access create post page
        create_response = session.get(create_url)
        create_soup = BeautifulSoup(create_response.text, 'html.parser')
        form = create_soup.find('form')
        
        if not form:
            return None
        
        # Find form fields
        csrf_input = create_soup.find('input', {'name': re.compile(r'csrf.*token', re.I)})
        csrf_token = csrf_input['value'] if csrf_input else None
        
        # Find title and content fields
        title_input = None
        content_input = None
        
        for input_field in create_soup.find_all(['input', 'textarea']):
            name = input_field.get('name', '')
            id_attr = input_field.get('id', '')
            
            if 'title' in name.lower() or 'title' in id_attr.lower():
                title_input = name
            elif any(term in name.lower() or term in id_attr.lower() for term in ['content', 'body', 'text']):
                content_input = name
        
        if not title_input or not content_input:
            return None
        
        # Prepare form data
        form_data = {
            title_input: post_data['title'],
            content_input: post_data['content']
        }
        
        # If there is a CSRF token, add to form data
        if csrf_token:
            form_data['csrf_token'] = csrf_token
        
        # Get form submission URL
        form_action = form.get('action')
        if not form_action:
            form_action = create_url
        elif not form_action.startswith('http'):
            form_action = urljoin(app_url, form_action)
        
        # Submit form
        submit_response = session.post(form_action, data=form_data, allow_redirects=True)
        
        # Return creation result
        return {
            'title': post_data['title'],
            'content': post_data['content'],
            'success': 'error' not in submit_response.url.lower(),
            'response': submit_response
        }
    except Exception as e:
        return {
            'title': post_data['title'],
            'content': post_data['content'],
            'success': False,
            'error': str(e)
        }

@pytest.fixture(scope="function")
def ensure_test_post(login_session, app_url, test_post_data, db_connection):
    """Ensure there is test post data, create one if not"""
    # Check if logged in
    if not getattr(login_session, 'is_logged_in', False):
        return None
    
    try:
        # Try to create a test post
        created_post = create_test_post(login_session, app_url, test_post_data)
        
        # If creating via UI fails, try to create directly in database
        if not created_post or not created_post.get('success'):
            from .utils import create_test_post_in_db
            post_id = create_test_post_in_db(db_connection, test_post_data)
            if post_id:
                return {
                    'title': test_post_data['title'],
                    'content': test_post_data['content'],
                    'success': True,
                    'db_created': True
                }
        
        # Return created post information
        return created_post
    except Exception as e:
        # Creation failed, return None
        return None