import base64
import logging
import os
import re
from functools import wraps
from hashlib import scrypt

import pyotp
from argon2 import PasswordHasher
from cryptography.fernet import Fernet
from flask import Flask, url_for, flash, redirect, render_template, request
from flask_admin import Admin
from flask_admin.contrib.sqla import Model<PERSON>ie<PERSON>
from flask_admin.menu import MenuLink
from flask_admin.theme import Bootstrap4Theme
from flask_login import LoginManager, UserMixin, current_user
from flask_qrcode import QRcode
from flask_sqlalchemy import SQLAlchemy
from flask_migrate import Migrate
from datetime import datetime
import secrets
from sqlalchemy import MetaData
from flask_limiter import Limiter
from flask_limiter.util import get_remote_address
from flask_bcrypt import Bcrypt
from dotenv import load_dotenv
from flask_talisman import Talisman

load_dotenv()

app = Flask(__name__)

# SECRET KEY FOR FLASK FORMS
app.config['SECRET_KEY'] = os.getenv('SECRET_KEY')
app.config['RECAPTCHA_PUBLIC_KEY'] = os.getenv('RECAPTCHA_PUBLIC_KEY')
app.config['RECAPTCHA_PRIVATE_KEY'] = os.getenv('RECAPTCHA_PRIVATE_KEY')

# DATABASE CONFIGURATION
app.config['SQLALCHEMY_DATABASE_URI'] = os.getenv('SQLALCHEMY_DATABASE_URI')
app.config['SQLALCHEMY_ECHO'] = os.getenv('SQLALCHEMY_ECHO') == 'True'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = os.getenv('SQLALCHEMY_TRACK_MODIFICATIONS') == 'True'

qrcode = QRcode(app)

metadata = MetaData(
    naming_convention={
        "ix": 'ix_%(column_0_label)s',
        "uq": "uq_%(table_name)s_%(column_0_name)s",
        "ck": "ck_%(table_name)s_%(constraint_name)s",
        "fk": "fk_%(table_name)s_%(column_0_name)s_%(referred_table_name)s",
        "pk": "pk_%(table_name)s"
    }
)

db = SQLAlchemy(app)
migrate = Migrate(app, db)


# DATABASE TABLES
class Post(db.Model):
    __tablename__ = 'posts'

    id = db.Column(db.Integer, primary_key=True)
    userid = db.Column(db.Integer, db.ForeignKey('users.id'))
    created = db.Column(db.DateTime, nullable=False)
    title = db.Column(db.Text, nullable=False)
    body = db.Column(db.Text, nullable=False)
    user = db.relationship("User", back_populates="posts")

    def __init__(self, userid, title, body):
        self.created = datetime.now()
        self.userid = userid
        self.title = encrypt(title)
        self.body = encrypt(body)

    def update(self, title, body):
        self.created = datetime.now()
        self.title = encrypt(title)
        self.body = encrypt(body)
        db.session.commit()

    def decrypt(self):
        self.title = decrypt(self.title, self.user)
        self.body = decrypt(self.body, self.user)


class User(db.Model, UserMixin):
    __tablename__ = 'users'

    id = db.Column(db.Integer, primary_key=True)

    # User authentication information.
    email = db.Column(db.String(100), nullable=False, unique=True)
    password = db.Column(db.String(100), nullable=False)

    # User information
    firstname = db.Column(db.String(100), nullable=False)
    lastname = db.Column(db.String(100), nullable=False)
    phone = db.Column(db.String(100), nullable=False)

    #RBAC
    role = db.Column(db.String(100), nullable=False)

    # User posts
    posts = db.relationship("Post", order_by=Post.id, back_populates="user")

    # User log
    log = db.relationship("Log", uselist=False, back_populates="user")

    # User MFA
    mfa_key = db.Column(db.String(32), nullable=False)
    mfa_enabled = db.Column(db.Boolean, nullable=False, default=False)

    # Encryption
    salt = db.Column(db.String(100), nullable=False)

    def __init__(self, email, firstname, lastname, phone, password):
        self.email = email
        self.firstname = firstname
        self.lastname = lastname
        self.phone = phone
        #self.password = bcrypt.generate_password_hash(password)
        self.password = ph.hash(password)
        self.role = 'end_user'
        self.mfa_key = pyotp.random_base32()
        self.salt = base64.b64encode(secrets.token_bytes(32)).decode()

        #print(self.salt)

    def verify_password(self, password):
        #return self.password == password
        return ph.verify(self.password, password)
        #return bcrypt.check_password_hash(self.password, password)

    def get_mfa_uri(self):
        return str(pyotp.totp.TOTP(self.mfa_key).provisioning_uri(self.email, 'CSC2031 Blog'))

    def verify_mfa(self, pin):
        return pyotp.TOTP(self.mfa_key).verify(pin)

    def generate_log(self):
        db.session.add(Log(self.id))
        db.session.commit()

    def generate_key(self):
        key = scrypt(
            password=self.password.encode(),  # Invokes .encode() to convert string input to bytes.
            salt=self.salt.encode(),
            n=2048,  # CPU Iterations (hardness)
            r=8,  # Memory Hardness
            p=1,  # Parallelisation Mode
            dklen=32  # Length of derived key in bytes (32 bytes = 256 bits)
        )
        return base64.b64encode(key)


class Log(db.Model):
    __tablename__ = 'logs'

    id = db.Column(db.Integer, primary_key=True)
    userid = db.Column(db.Integer, db.ForeignKey('users.id'))
    registered_on = db.Column(db.DateTime, nullable=False)
    latest_login = db.Column(db.DateTime, nullable=True)
    previous_login = db.Column(db.DateTime, nullable=True)
    latest_ip = db.Column(db.String(100), nullable=True)
    previous_ip = db.Column(db.String(100), nullable=True)

    user = db.relationship("User", back_populates="log")

    def __init__(self, userid):
        self.userid = userid
        self.registered_on = datetime.now()


# DATABASE ADMINISTRATOR
class PostView(ModelView):
    # def is_accessible(self):
    #     return current_user.is_authenticated and current_user.role == 'db_admin'
    #
    # def inaccessible_callback(self, name, **kwargs):
    #     if current_user.is_authenticated:
    #         return render_template('errors/403.html')
    #
    #     flash('Administrator access required', category="danger")
    #     return redirect(url_for('accounts.login'))

    # can_create = False
    # can_edit = False
    # can_delete = False

    column_display_pk = True  # optional, but I like to see the IDs in the list
    column_hide_backrefs = False
    column_list = ('id', 'userid', 'created', 'title', 'body', 'user')


class UserView(ModelView):
    # def is_accessible(self):
    #     return current_user.is_authenticated and current_user.role == 'db_admin'
    #
    # def inaccessible_callback(self, name, **kwargs):
    #     if current_user.is_authenticated:
    #         return render_template('errors/403.html')
    #
    #     flash('Administrator access required', category="danger")
    #     return redirect(url_for('accounts.login'))

    #form_excluded_columns = ('salt')

    column_display_pk = True  # optional, but I like to see the IDs in the list
    column_hide_backrefs = False
    column_list = (
        'id', 'email', 'password', 'firstname', 'lastname', 'phone', 'role', 'posts', 'mfa_key', 'mfa_enabled')


class LogView(ModelView):
    column_display_pk = True  # optional, but I like to see the IDs in the list
    column_hide_backrefs = False
    column_list = (
        'id', 'userid', 'registered_on', 'latest_login', 'previous_login', 'latest_ip', 'previous_ip', 'user')


class MainIndexLink(MenuLink):
    def get_url(self):
        return url_for("index")


admin = Admin(app, name='DB Admin', theme=Bootstrap4Theme(fluid=True))
admin._menu = admin._menu[1:]  # removes home tab
admin.add_link(MainIndexLink(name='Home'))
admin.add_view(PostView(Post, db.session))
admin.add_view(UserView(User, db.session))
admin.add_view(LogView(Log, db.session))

# RATE LIMITING
limiter = Limiter(key_func=get_remote_address, app=app, default_limits=[], enabled=False)

# LOGIN MANAGEMENT
login_manager = LoginManager()
login_manager.login_view = 'accounts.login'
login_manager.login_message_category = 'info'
login_manager.init_app(app)


@login_manager.user_loader
def load_user(id):
    return User.query.get(int(id))


#RBAC
def roles_required(*roles):
    def inner_decorator(f):
        @wraps(f)
        def wrapped(*args, **kwargs):
            if current_user.role not in roles:
                logger.warning('[User:{}, Role:{}, URL:{}, IP:{}] Unauthorised Access Attempt'.format(
                    current_user.email,
                    current_user.role,
                    request.url,
                    request.remote_addr))
                return render_template('errors/403.html')
            return f(*args, **kwargs)

        return wrapped

    return inner_decorator


# EVENT LOGGING
logger = logging.getLogger('security_logger')
handler = logging.FileHandler('security.log', 'a')
handler.setLevel(logging.WARNING)
formatter = logging.Formatter('%(asctime)s : %(message)s', '%d/%m/%Y %I:%M:%S %p')
handler.setFormatter(formatter)
logger.addHandler(handler)

# ENCRYPTION
#bcrypt = Bcrypt(app)
ph = PasswordHasher()


def encrypt(plaintext):
    cipher = Fernet(current_user.generate_key())
    return cipher.encrypt(plaintext.encode()).decode()


def decrypt(ciphertext, user):
    cipher = Fernet(user.generate_key())
    return cipher.decrypt(ciphertext).decode()


# Define a set of rules to filter out malicious requests
rules = {
    'sql_injection': re.compile('.*(union|select|insert|drop|alter|;|`|\')', re.IGNORECASE),
    'xss_attack': re.compile('.*(<script[^>]*>|<iframe[^>]*>|%3Cscript[^%>]*%3E|%3Ciframe[^%>]*%3E|javascript:)', re.IGNORECASE),
    'path_traversal': re.compile('.*(\.\./|\.\.)', re.IGNORECASE)
}


@app.before_request
def check_request_for_attacks():
    for attack_type, pattern in rules.items():
        # If any of the rules match, we block the request

        if pattern.search(request.path) or pattern.search(request.query_string.decode()):
            return render_template('errors/firewall.html', attack_type=attack_type)


# SECURITY HEADERS
csp = {
    # 'default-src': ['\'self\''],
    'style-src': ['\'self\'',
                  'https://cdn.jsdelivr.net/npm/bootstrap@4.6.2/dist/css/bootstrap.min.css',
                    ],
    'script-src': ['\'self\'',
                   'https://www.google.com/recaptcha/',
                   'https://www.gstatic.com/recaptcha/',
                    'https://cdn.jsdelivr.net/npm/bootstrap@5.2.2/dist/js/bootstrap.bundle.min.js'
                   ],
    'frame-src': ['\'self\'',
                  'https://www.google.com/recaptcha/',
                  'https://recaptcha.google.com/recaptcha/'
                ]
}


talisman = Talisman(app, content_security_policy=csp)


# IMPORT BLUEPRINTS
from accounts.views import accounts_bp
from posts.views import posts_bp
from security.views import security_bp

# REGISTER BLUEPRINTS
app.register_blueprint(accounts_bp)
app.register_blueprint(posts_bp)
app.register_blueprint(security_bp)
